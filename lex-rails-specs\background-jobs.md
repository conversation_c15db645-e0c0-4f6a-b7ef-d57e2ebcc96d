Background Job

1. <PERSON><PERSON> Scraping
    - Frequency: On Demand
2. ASIN Scraping
    - Frequency: On Demand and Weekly(if client)
3. Review Scraping
    - Frequency: On Demand
4. Review Checker 
    - Frequency: On Demand and Daily (based on run_frequency)
5. Prompt Job (AI Analysis)
    - On Demand
6. Review Image Generation 
    - On Demand Or Client
7. Prompt Testing Job
    - On Demand

On client Change:
- Gen SS
- change the run frequency 
    - client: 3
    - client+violation: 1

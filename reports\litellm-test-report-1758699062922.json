{"timestamp": "2025-09-24T07:30:20.884Z", "availableModels": [{"id": "gemini/gemini-2.5-flash", "object": "model", "created": 1677610602, "owned_by": "openai"}, {"id": "gemini/gemini-2.5-pro", "object": "model", "created": 1677610602, "owned_by": "openai"}, {"id": "gpt-4.1-mini", "object": "model", "created": 1677610602, "owned_by": "openai"}, {"id": "gpt-4.1", "object": "model", "created": 1677610602, "owned_by": "openai"}, {"id": "gpt-4o", "object": "model", "created": 1677610602, "owned_by": "openai"}, {"id": "grok-3-mini", "object": "model", "created": 1677610602, "owned_by": "openai"}], "functionTests": {"getChatGPTResponse": [{"testCase": "Amazon Product Analysis", "success": true, "responseTime": 2030, "tokenUsage": {"promptTokens": 308, "completionTokens": 8, "totalTokens": 316}, "response": "men's air cushioned running shoes...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:getChatGPTResponse", "case:Amazon Product Analysis"]}}, {"testCase": "Company Name Analysis", "success": true, "responseTime": 5509, "tokenUsage": {"promptTokens": 23, "completionTokens": 256, "totalTokens": 279}, "response": "**Nike Inc.** is a globally recognized company that operates primarily in the athletic footwear, app...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:getChatGPTResponse", "case:Company Name Analysis"]}}, {"testCase": "Product Description", "success": true, "responseTime": 1055, "tokenUsage": {"promptTokens": 33, "completionTokens": 21, "totalTokens": 54}, "response": "Wireless noise-canceling headphones offering immersive sound and a 30-hour battery life for prolonge...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:getChatGPTResponse", "case:Product Description"]}}, {"testCase": "Translation Task", "success": true, "responseTime": 935, "tokenUsage": {"promptTokens": 24, "completionTokens": 7, "totalTokens": 31}, "response": "Hello, how are you?...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:getChatGPTResponse", "case:Translation Task"]}}, {"testCase": "Content Analysis", "success": true, "responseTime": 1201, "tokenUsage": {"promptTokens": 32, "completionTokens": 36, "totalTokens": 68}, "response": "The sentiment of this text is **positive**. The use of phrases like \"absolutely amazing\" and \"best p...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:getChatGPTResponse", "case:Content Analysis"]}}], "completionFactory": [{"testCase": "Competition Search Keyword", "type": "compSearchKeyword", "success": true, "responseTime": 1396, "tokenUsage": {"promptTokens": 305, "completionTokens": 10, "totalTokens": 315}, "response": "Men's cushioned running shoes for daily wear...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:completionFactory", "type:compSearchKeyword"]}}, {"testCase": "Product Title Humanization", "type": "productTitleHumanisation", "success": true, "responseTime": 1106, "tokenUsage": {"promptTokens": 1043, "completionTokens": 7, "totalTokens": 1050}, "response": "iPhone 15 Pro Max...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:completionFactory", "type:productTitleHumanisation"]}}, {"testCase": "Company Name Humanization", "type": "companyNameHumanisation", "success": true, "responseTime": 973, "tokenUsage": {"promptTokens": 471, "completionTokens": 2, "totalTokens": 473}, "response": "Nike...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:completionFactory", "type:companyNameHumanisation"]}}, {"testCase": "BSR Category Detection", "type": "bsrCategoryUsingGpt", "success": true, "responseTime": 1000, "tokenUsage": {"promptTokens": 167, "completionTokens": 6, "totalTokens": 173}, "response": "Over-Ear Headphones...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:completionFactory", "type:bsrCategoryUsingGpt"]}}, {"testCase": "PPC Audit Analysis", "type": "ppc<PERSON><PERSON><PERSON>", "success": true, "responseTime": 1078, "tokenUsage": {"promptTokens": 655, "completionTokens": 33, "totalTokens": 688}, "response": "Branded mid-tail keyword - nike iphone 15 pro max  \nNon-branded long-tail keyword - unlocked apple i...", "metadata": {"tags": ["jeff", "scrapeGPT", "getChatGPTResponse", "test:completionFactory", "type:ppcAudit"]}}], "multiProviderAI": [{"testCase": "Content Analysis", "model": "azure-gpt4o", "success": true, "responseTime": 5792, "tokenUsage": {"promptTokens": 42, "completionTokens": 276, "totalTokens": 318}, "response": "This review is concise, positive, and provides a clear endorsement of the product. Here's a breakdow...", "provider": "azure-openai"}, {"testCase": "Product Categorization", "model": "azure-gpt4o", "success": true, "responseTime": 1343, "tokenUsage": {"promptTokens": 45, "completionTokens": 9, "totalTokens": 54}, "response": "Category: Electronics > Mobile Phones > Smartphones...", "provider": "azure-openai"}, {"testCase": "Sentiment Analysis", "model": "azure-gpt4o", "success": true, "responseTime": 1166, "tokenUsage": {"promptTokens": 29, "completionTokens": 27, "totalTokens": 56}, "response": "The sentiment of the statement is **positive**. The phrase \"exceeded my expectations\" conveys satisf...", "provider": "azure-openai"}, {"testCase": "Keyword Extraction", "model": "azure-gpt4o", "success": true, "responseTime": 1707, "tokenUsage": {"promptTokens": 64, "completionTokens": 33, "totalTokens": 97}, "response": "Nike Air Max 270, running shoes, ultimate comfort, visible Air Max unit, maximum cushioning, running...", "provider": "azure-openai"}, {"testCase": "Text Summarization", "model": "azure-gpt4o", "success": true, "responseTime": 1334, "tokenUsage": {"promptTokens": 65, "completionTokens": 38, "totalTokens": 103}, "response": "TechCorp, founded in 2010, is a leading tech company specializing in innovative consumer electronics...", "provider": "azure-openai"}], "humanizationFunctions": [{"function": "humanizeCompanyName", "input": "NIKE, INC.", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeCompanyName", "input": "Apple Computer Corp.", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeCompanyName", "input": "Amazon.com LLC", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeCompanyName", "input": "Microsoft Corporation", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeCompanyName", "input": "Tesla Motors Inc.", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeProductTitle", "input": "Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeProductTitle", "input": "Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeProductTitle", "input": "Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeProductTitle", "input": "Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"function": "humanizeProductTitle", "input": "Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS", "success": false, "error": "Error fetching text from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}], "getKeyword": [{"testCase": "Amazon Product Data", "success": false, "error": "Error fetching Keyword from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"testCase": "About Data Only", "success": false, "error": "Error fetching Keyword from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"testCase": "Electronics Product", "success": false, "error": "Error fetching Keyword from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"testCase": "Home & Kitchen", "success": false, "error": "Error fetching Keyword from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}, {"testCase": "Fashion Product", "success": false, "error": "Error fetching Keyword from ChatGPT: Error: Error fetching response from ChatGPT: TypeError: Cannot read properties of undefined (reading 'model')"}], "runPromptChain": [{"testCase": "Brand Analysis Chain", "success": true, "responseTime": 1881, "tokenUsage": {"promptTokens": 111, "completionTokens": 13, "totalTokens": 124}, "cost": 0, "output": "KEY: Performance running shoes..."}, {"testCase": "Product Analysis Chain", "success": true, "responseTime": 10363, "tokenUsage": {"promptTokens": 27, "completionTokens": 813, "totalTokens": 840}, "cost": 0, "output": "### Analysis of Wireless Bluetooth Headphones Market Position\n\nWireless Bluetooth headphones have un..."}, {"testCase": "Content Review Chain", "success": true, "responseTime": 1948, "tokenUsage": {"promptTokens": 45, "completionTokens": 91, "totalTokens": 136}, "cost": 0, "output": "The content is concise, relevant, and provides a positive assessment of the headphones' sound qualit..."}]}, "summary": {"totalTests": 33, "successfulTests": 18, "failedTests": 15, "totalTokensUsed": 4075, "totalCost": 0, "successRate": "54.55%", "averageTokensPerTest": 123, "estimatedCost": "0.0815"}}
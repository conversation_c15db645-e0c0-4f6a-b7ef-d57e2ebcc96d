Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to ... | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers f...",308,8,0
getChatGPTResponse,gpt-4o,"System: Analyze the following company name and provide insights.... | User: Nike Inc.......",23,256,0
getChatGPTResponse,gpt-4o,"System: Summarize the following product in one sentence.... | User: Wireless noise-canceling headphones with 30-hour battery life......",33,21,0
getChatGPTResponse,gpt-4o,"System: Translate the following text to English:... | User: Bonjour, comment allez-vous?......",24,7,0
getChatGPTResponse,gpt-4o,"System: Analyze the sentiment of the following text.... | User: This product is absolutely amazing! Best purchase I have ever made.......",32,36,0
completionFactory,gpt-4o,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""desc......",305,10,0
completionFactory,gpt-4o,"Factory Type: productTitleHumanisation | Data: ""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""......",1043,7,0
completionFactory,gpt-4o,"Factory Type: companyNameHumanisation | Data: ""NIKE, INC.""......",471,2,0
completionFactory,gpt-4o,"Factory Type: bsrCategoryUsingGpt | Data: ""Wireless Bluetooth Headphones with Noise Cancellation""......",167,6,0
completionFactory,gpt-4o,"Factory Type: ppcAudit | Data: {""brandName"":""Nike"",""productTitle"":""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartpho......",655,33,0
multiProviderAI,azure-gpt4o,"System: You are an expert content analyst.... | User: Analyze this review: ""These headphones have amazin......",42,247,0
multiProviderAI,azure-gpt4o,"System: You are a product categorization expert.... | User: Categorize this product: ""Apple iPhone 15 Pro Max ......",45,9,0
multiProviderAI,azure-gpt4o,"System: You are a sentiment analysis expert.... | User: Analyze the sentiment: ""This product exceeded my e......",29,27,0
multiProviderAI,azure-gpt4o,"System: You are a keyword extraction expert.... | User: Extract keywords from: ""Experience ultimate comfor......",64,48,0
multiProviderAI,azure-gpt4o,"System: You are a text summarization expert.... | User: Summarize: ""TechCorp is a leading technology compa......",65,39,0
humanizationFunctions,gpt-4o,"Humanize company name: NIKE, INC....",476,2,0
humanizationFunctions,gpt-4o,"Humanize company name: Apple Computer Corp....",475,3,0
humanizationFunctions,gpt-4o,"Humanize company name: Amazon.com LLC...",474,2,0
humanizationFunctions,gpt-4o,"Humanize company name: Microsoft Corporation...",473,2,0
humanizationFunctions,gpt-4o,"Humanize company name: Tesla Motors Inc....",475,3,0
humanizationFunctions,gpt-4o,"Humanize product title: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System......",1064,7,0
humanizationFunctions,gpt-4o,"Humanize product title: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000......",1070,9,0
humanizationFunctions,gpt-4o,"Humanize product title: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life......",1066,7,0
humanizationFunctions,gpt-4o,"Humanize product title: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel......",1061,4,0
humanizationFunctions,gpt-4o,"Humanize product title: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS......",1059,10,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""desc......",308,8,0
getKeyword,gpt-4o,"Extract keyword from: {""aboutData"":""TechCorp is a leading technology company specializing in innovative consumer electroni......",291,6,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with ......",271,11,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure ......",272,8,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Levi's 501 Original Fit Jeans for Men"",""description"":""Classic straight leg jeans made from......",270,9,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: >     You will receive a brand name or product keyword. Your job is to an......",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""Analyze the following product and provide insights about its market posi......",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""You are a content reviewer. Analyze the following content for quality an......",N/A,N/A,0
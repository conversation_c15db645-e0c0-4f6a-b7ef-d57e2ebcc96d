Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}",308,8,0
getChatGPTResponse,gpt-4o,"System: Analyze the following company name and provide insights. | User: Nike Inc.",23,256,0
getChatGPTResponse,gpt-4o,"System: Summarize the following product in one sentence. | User: Wireless noise-canceling headphones with 30-hour battery life",33,21,0
getChatGPTResponse,gpt-4o,"System: Translate the following text to English: | User: Bonjour, comment allez-vous?",24,7,0
getChatGPTResponse,gpt-4o,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.",32,36,0
completionFactory,gpt-4o,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}",305,10,0
completionFactory,gpt-4o,"Factory Type: productTitleHumanisation | Data: ""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""",1043,7,0
completionFactory,gpt-4o,"Factory Type: companyNameHumanisation | Data: ""NIKE, INC.""",471,2,0
completionFactory,gpt-4o,"Factory Type: bsrCategoryUsingGpt | Data: ""Wireless Bluetooth Headphones with Noise Cancellation""",167,6,0
completionFactory,gpt-4o,"Factory Type: ppcAudit | Data: {""brandName"":""Nike"",""productTitle"":""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""}",655,33,0
multiProviderAI,azure-gpt4o,"System: You are an expert content analyst. | User: Analyze this review: ""These headphones have amazing sound quality and the battery lasts all day. Highly recommend for music lovers.""",42,437,0
multiProviderAI,gpt-4.1-jeff,"System: You are a product categorization expert. | User: Categorize this product: ""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""",45,12,0
multiProviderAI,gpt-4.1-mini-jeff,"System: You are a sentiment analysis expert. | User: Analyze the sentiment: ""This product exceeded my expectations!""",29,29,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.",476,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.",475,3,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC",474,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation",473,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.",475,3,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System",1064,7,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000",1070,9,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life",1066,7,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel",1061,4,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS",1059,10,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}",308,8,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""aboutData"":""TechCorp is a leading technology company specializing in innovative consumer electronics and smart home solutions. Founded in 2010, we focus on creating products that enhance daily life through cutting-edge technology and user-friendly design.""}",291,6,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}",271,11,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}",272,8,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Levi's 501 Original Fit Jeans for Men"",""description"":""Classic straight leg jeans made from premium denim""}",270,9,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: >     You will receive a brand name or product keyword. Your job is to analyze what type of product it represents.     Think about what niche or differentiator it might have and output a short search phrase that would return an exact competitor.  - role: user   content: ""{{input}}""  - role: system   content: >     Humanize the phrase by making it more natural and marketable. Add the prefix 'KEY:' at the beginning.     Do not include the brand name. Don't add anything else.  - role: user   content: ""{{input}}""       ",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""Analyze the following product and provide insights about its market position.""  - role: user   content: ""{{productName}}""       ",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""You are a content reviewer. Analyze the following content for quality and relevance.""  - role: user   content: ""{{content}}""       ",N/A,N/A,0
Function Name,Model Name,Prompt Used,Response,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,gemini/gemini-2.5-flash,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's running shoes maximum cushioning",333,651,644
getChatGPTResponse,gemini/gemini-2.5-flash,"System: Analyze the following company name and provide insights. | User: Nike Inc.","""Nike Inc."" is one of the most iconic and strategically brilliant company names in business history. Here's an analysis and insights:  1.  **Origin and Meaning:**     *   **Greek Goddess of Victory:** The name",13,1022,974
getChatGPTResponse,gemini/gemini-2.5-flash,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text is **Extremely Positive**.  Here's why:  *   **""absolutely amazing!""**: ""Amazing"" is a very strong positive adjective, and ""absolutely"" intensifies it further. The exclamation mark emphasizes strong emotion. *   **""Best purchase I have ever made.""**: ""Best"" is a superlative, indicating the highest possible satisfaction or quality. ""Ever made"" emphasizes that this is a top-tier experience for the speaker.",22,463,366
getChatGPTResponse,gemini/gemini-2.5-pro,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","N/A",333,1023,1023
getChatGPTResponse,gemini/gemini-2.5-pro,"System: Analyze the following company name and provide insights. | User: Nike Inc.","N/A",13,1023,1023
getChatGPTResponse,gemini/gemini-2.5-pro,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text is **overwhelmingly positive**.  Here's a breakdown of the analysis:  *   **""Absolutely amazing!""**: ""Amazing"" is a very strong positive adjective, and ""absolutely"" acts as an intensifier, making the sentiment even stronger. The exclamation mark adds a layer of enthusiasm. *   **""Best purchase I have ever made.""**: The word ""best"" is a superlative, indicating the highest possible degree of satisfaction. The phrase ""ever made"" reinforces this, suggesting this purchase surpasses all others in the person's experience.  Overall, the text expresses extreme satisfaction and enthusiasm for the product.",22,805,673
getChatGPTResponse,gpt-4.1-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's cushioned running shoes with visible air unit",308,11,0
getChatGPTResponse,gpt-4.1-mini,"System: Analyze the following company name and provide insights. | User: Nike Inc.","Nike Inc. is a globally recognized multinational corporation specializing in the design, manufacturing, and marketing of athletic footwear, apparel, equipment, and accessories. Founded in 1964 as Blue Ribbon Sports and officially becoming Nike, Inc. in 1971, the company has grown to become one of the world's leading brands in the sportswear industry.  Key Insights:  1. Brand Recognition: Nike's iconic ""Swoosh"" logo and ""Just Do It"" slogan are among the most recognizable brand elements worldwide, symbolizing performance, inspiration, and innovation.  2. Product Range: Nike offers a broad spectrum of products catering to various sports and fitness activities, including running, basketball, soccer, and training, appealing to both professional athletes and everyday consumers.  3. Innovation Focus: The company invests heavily in research and development to introduce cutting-edge technologies in footwear and apparel, enhancing athletic performance and comfort.  4. Global Presence: Nike operates in numerous countries with a comprehensive distribution network, including retail stores, online platforms, and partnerships with third-party retailers.  5. Market Position: As a market leader, Nike competes with other major sportswear brands like Adidas and Under Armour, maintaining a strong market share through marketing, endorsements by high-profile athletes, and continuous product innovation.  6. Corporate Responsibility: Nike has made strides in sustainability and ethical manufacturing practices, although it has faced scrutiny in the past regarding labor practices.  In summary, Nike Inc. is a dominant player in the global sportswear market known for its strong brand identity, innovative products, and widespread consumer appeal.",23,312,0
getChatGPTResponse,gpt-4.1-mini,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text is very positive. The user expresses strong satisfaction and enthusiasm about the product, describing it as ""absolutely amazing"" and the ""best purchase"" they have ever made.",32,40,0
getChatGPTResponse,gpt-4.1,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air max cushioned running shoes",308,9,0
getChatGPTResponse,gpt-4.1,"System: Analyze the following company name and provide insights. | User: Nike Inc.","Certainly! Here's an analysis of the company name **Nike Inc.**:  ---  **1. Name Origin and Meaning:**   - **Nike** (pronounced ""NY-kee"") is derived from the Greek goddess of victory. The choice of name signifies excellence, triumph, and inspiration, aligning with the brand's focus on athletic success.  **2. Brand Recognition:**   - The name Nike is universally recognized and associated with sports, performance, and innovation. Its simplicity, memorability, and strong mythological ties have contributed greatly to its global appeal.  **3. Corporate Structure:**   - The suffix **Inc.** stands for ""Incorporated,"" indicating that Nike is a legally incorporated company, providing it with certain legal protections and the ability to issue stock.  **4. Market Position:**   - ""Nike Inc."" as a name is synonymous with leadership in athletic footwear, apparel, and equipment. It conveys both ambition and achievement.  **5. Marketing and Psychology:**   - The word ""Nike"" is short and easy to pronounce in many languages, aiding international branding. The association with victory is motivational, which is crucial in the sports industry.  **6. Symbolism and Legacy:**   - Over decades, ""Nike Inc."" has evolved beyond a company name—it represents a lifestyle, culture, and even social movements through its endorsements and campaigns.  **7. Logo Connection:**   - The famous ""Swoosh"" logo complements the energy and movement implied by the name, reinforcing the concept of speed, agility, and victory.  ---  **Summary:**   The name **Nike Inc.** is powerful because it combines historical resonance (Greek mythology), clear corporate structure (Inc.), and broad marketing appeal. It is a prime example of a company name that elevates a brand to iconic status through its strategic selection, cultural associations, and consistent messaging.",23,380,0
getChatGPTResponse,gpt-4.1,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text is highly positive. The use of phrases like ""absolutely amazing"" and ""best purchase I have ever made"" demonstrates strong satisfaction and enthusiasm about the product.",32,38,0
getChatGPTResponse,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air cushioned running shoes",308,8,0
getChatGPTResponse,gpt-4o,"System: Analyze the following company name and provide insights. | User: Nike Inc.","### Analysis and Insights for ""Nike Inc.""  **1. Brand Overview:** - **Name Origin:** Nike Inc. is named after the Greek goddess of victory, ""Nike"" (pronounced ""Nee-Kay""), which aligns with the company's focus on athletic performance and success. - **Incorporation:** The addition of ""Inc."" indicates that Nike is a publicly traded corporation, legally structured to limit liability and raise capital through shareholders.  ---  **2. Industry and Market Position:** - **Sector:** Sportswear and athletic equipment. Nike specializes in designing, manufacturing, and selling high-performance footwear, apparel, equipment, and accessories. - **Global Leader:** Nike is the world's largest supplier in the athletic footwear and apparel industry, known for its innovative product designs and aggressive marketing strategies.  ---  **3. Branding and Messaging:** - **Core Philosophy:** ""Just Do It"" reflects empowerment, resilience, and motivation, aligning with the company's mission to inspire athletes and promote a culture of determination and performance. - **Logo:** The ""Swoosh"" symbolizes motion, speed, and fluidity, reinforcing Nike's connection to athleticism and momentum. - **Emotional Appeal:** Nike connects with consumers through storytelling, inclusivity, and aspirational themes.  ---  **4. Competitive Edge:** - **Innovation:** Nike consistently pioneers technology-driven products, such as Flyknit, ZoomX, Adapt self-lacing shoes, and sustainable materials. - **Global Presence:** With operations in over 170 countries, Nike has built a robust supply chain and distribution network. - **Celebrity Collaborations:** Strategic endorsements from top athletes and celebrities (e.g., Michael Jordan, Serena Williams, LeBron James) drive brand loyalty and awareness.  ---  **5. Challenges:** - **Sustainability Pressures:** As the industry shifts toward sustainable production, Nike faces scrutiny regarding environmental impact and supply chain ethics. - **Competition:** As a dominant player, Nike competes intensely with Adidas, Puma, and Under Armour, as well as with emerging smaller brands. - **Cultural Expectations:** Nike has engaged in social and political conversations, which can be a double-edged sword, appealing to some consumers while alienating others.  ---  **6. Financial Performance:** - **Revenue Leader:** With annual multi-billion-dollar revenues, Nike is a dominant force in the sportswear market. - **E-commerce Growth:** The shift toward direct-to-consumer (DTC) sales via Nike's website and apps is a key driver of growth.  ---  **7. Innovations in",23,512,0
getChatGPTResponse,gpt-4o,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text is **highly positive**.",32,13,0
getChatGPTResponse,grok-3-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air cushioned running shoes with cushioning",309,9,860
getChatGPTResponse,grok-3-mini,"System: Analyze the following company name and provide insights. | User: Nike Inc.","### Analysis of the Company Name: Nike Inc.  Thank you for your query. I'll analyze the company name ""Nike Inc."" by breaking it down into its components, exploring its origins, branding implications, and broader insights. This analysis draws on the historical and cultural context of the name to provide a comprehensive understanding.  #### 1. **Breakdown of the Name**    - **Nike**: This is the core brand name, derived from Greek mythology. ""Nike"" refers to the ancient Greek goddess of victory, often depicted with wings symbolizing speed, triumph, and achievement. The name was chosen in 1971 by Nike's co-founder Phil Knight and the company's first employee, Jeff Johnson, during a brainstorming session. It replaced the original company name, Blue Ribbon Sports, which was the predecessor to Nike Inc.    - **Inc.**: This suffix stands for ""Incorporated,"" a legal designation indicating that the company is a corporation registered in the United States. It denotes a business structure where shareholders have limited liability, and it's common for publicly traded companies. Nike Inc. was incorporated in 1972 and became a publicly traded entity on the New York Stock Exchange in 1980.  #### 2. **Origins and Historical Context**    - Nike Inc. began as a distributor of running shoes in 1964, founded by Phil Knight and Bill Bowerman. The rebranding to ""Nike"" in 1971 was a pivotal moment, aligning the company with a name that evoked inspiration and aspiration. The choice was strategic, as it was short, memorable, and easy to pronounce across languages, which has contributed to its global appeal.    - Historically, the name has roots in a time when athletic wear was gaining popularity, and Nike positioned itself as a symbol of athletic excellence. The ""Swoosh"" logo, designed by Carolyn Davidson in 1971, complements the name by visually representing motion and victory, further reinforcing the brand's identity.  #### 3. **Branding and Marketing Insights**    - **Strengths of the Name**:      - **Memorability and Emotional Resonance**: ""Nike"" is concise and powerful, evoking emotions of success and determination. This aligns perfectly with the company's marketing strategy, which often features high-profile athletes and motivational campaigns like ""Just Do It"" (launched in 1988). The name helps create a strong emotional connection with consumers, positioning Nike as more than just a product—it's a lifestyle brand.      - **Global Adaptability**: The name transcends cultural boundaries, making it easy to market internationally. Nike has built a brand worth over $34 billion (as of recent estimates), largely due to its universal appeal. For instance, in advertising, the name is often paired with imagery of victory, reinforcing its mythological origins.      - **Innovation and Evolution**: Over time, the name has adapted to new markets, such as Nike's expansion into digital fitness apps (e.g., Nike Training Club) and sustainability initiatives. This shows how a simple name can evolve with the company's growth without needing rebranding.     - **Potential Challenges**:      - **Cultural Sensitivity**: While the name draws from Greek mythology, it has occasionally faced scrutiny in discussions about cultural appropriation or over-commercialization of historical symbols. However, this hasn't significantly impacted the brand's popularity.      - **Saturation and Imitation**: As one of the most recognized brands worldwide, ""Nike"" is often imitated by counterfeiters, which can dilute its exclusivity. The company invests heavily in legal protections to safeguard the name.  #### 4. **Broader Business and Market Insights**    - **Market Position**: Nike Inc. is a dominant player in the global sportswear industry, with a market capitalization exceeding $150 billion as of 2023. The name has played a key role in establishing Nike as a leader in innovation, with products ranging from footwear to apparel and digital services. Insights from brand valuation studies (e.g., by Interbrand) highlight that ""Nike"" contributes significantly to the company's intangible assets, driving customer loyalty and premium pricing.    - **Cultural Impact**: Beyond business, the name ""Nike"" has permeated popular culture, appearing in music, art, and social movements. For example, endorsements from athletes like Michael Jordan and Serena Williams have amplified the name's association with excellence. During events like the Olympics, Nike's branding often ties back to the ""goddess of victory"" theme, enhancing its relevance.    - **Lessons for Other Brands**: From a strategic perspective, Nike's name exemplifies how a well-chosen brand identity can foster long-term success. It demonstrates the importance of selecting a name that is timeless, aspirational, and adaptable, which has helped Nike navigate economic shifts, such as the rise of e-commerce and the focus on sustainability.  #### Summary The name ""Nike Inc."" is a masterful blend of mythology and modernity, symbolizing victory and achievement while providing a solid corporate foundation. It has been instrumental in building one of the world's most valuable brands, with insights revealing its role in emotional marketing, global expansion, and cultural influence. For businesses, this name serves as a case study in how a simple, evocative choice can drive decades of growth and innovation. If you'd like to dive deeper into specific aspects, such as marketing strategies or competitive analysis, let me know!",23,1073,509
getChatGPTResponse,grok-3-mini,"System: Analyze the sentiment of the following text. | User: This product is absolutely amazing! Best purchase I have ever made.","The sentiment of the text ""This product is absolutely amazing! Best purchase I have ever made."" is overwhelmingly positive.  ### Analysis: - **Key indicators of positivity**:    - Words like ""amazing"" and ""best"" express strong admiration and satisfaction.   - The adverb ""absolutely"" intensifies the positive emotion, emphasizing the user's enthusiasm.   - The exclamation mark (!) adds excitement and energy, reinforcing a highly favorable tone. - **Overall tone**: The text conveys delight and high recommendation, with no elements of negativity or neutrality. This suggests the user is extremely happy with their purchase.",32,120,352
completionFactory,gpt-4o,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","Men's cushioned running shoes for daily wear",305,10,0
completionFactory,gpt-4o,"Factory Type: productTitleHumanisation | Data: ""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""","iPhone 15 Pro Max",1043,7,0
completionFactory,gpt-4o,"Factory Type: companyNameHumanisation | Data: ""NIKE, INC.""","Nike",471,2,0
completionFactory,gpt-4o,"Factory Type: bsrCategoryUsingGpt | Data: ""Wireless Bluetooth Headphones with Noise Cancellation""","Over-Ear Headphones",167,6,0
completionFactory,gpt-4o,"Factory Type: ppcAudit | Data: {""brandName"":""Nike"",""productTitle"":""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""}","Branded mid-tail keyword - nike iphone 15 pro max   Non-branded long-tail keyword - unlocked apple iphone 15 pro max 256gb natural titanium",655,33,0
multiProviderAI,azure-gpt4o,"System: You are an expert content analyst. | User: Analyze this review: ""These headphones have amazing sound quality and the battery lasts all day. Highly recommend for music lovers.""","This review is highly positive and concise, highlighting two key features of the headphones: **sound...",42,251,0
multiProviderAI,gpt-4.1-jeff,"System: You are a product categorization expert. | User: Categorize this product: ""Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System""","**Category:**   Electronics > Mobile Phones > Smartphones  **Full Categorization Breakdown:**   - El...",45,52,0
multiProviderAI,gpt-4.1-mini-jeff,"System: You are a sentiment analysis expert. | User: Analyze the sentiment: ""This product exceeded my expectations!""","The sentiment of the sentence ""This product exceeded my expectations!"" is positive. The phrase indic...",29,28,0
multiProviderAI,gemini-2.5-flash,"System: You are a keyword extraction expert. | User: Extract keywords from: ""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""","Here are the keywords extracted from the text:  *   Nike Air Max 270 *   running shoes *   ultimate ...",75,62,0
multiProviderAI,gemini-2.5-pro,"System: You are a text summarization expert. | User: Summarize: ""TechCorp is a leading technology company specializing in innovative consumer electronics and smart home solutions. Founded in 2010, we focus on creating products that enhance daily life through cutting-edge technology and user-friendly design.""","TechCorp, founded in 2010, is a technology company that creates user-friendly consumer electronics a...",77,31,0
litellmGatewayModels,gemini/gemini-2.5-flash,"System: You are a product analysis expert. Analyze the given product. | User: Analyze this product: ""Wireless Bluetooth Headphones with Noise Cancellation""","N/A",25,399,399
litellmGatewayModels,gemini/gemini-2.5-pro,"System: You are a content summarization expert. | User: Summarize: ""This product has excellent build quality and amazing sound. The battery lasts all day and the noise cancellation works perfectly.""","N/A",35,399,399
litellmGatewayModels,grok-3-mini,"System: You are a product analysis expert. Analyze the given product. | User: Analyze this product: ""Wireless Bluetooth Headphones with Noise Cancellation""","N/A",36,N/A,400
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",523,239,238
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",522,722,720
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",522,982,981
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",520,351,350
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla",522,519,518
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",523,615,614
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",522,711,709
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",522,714,713
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",520,456,455
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla Motors",522,704,702
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",476,2,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",475,3,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",474,2,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",473,2,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla Motors",475,3,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",476,2,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",475,3,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",474,2,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",473,2,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla Motors",475,3,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",476,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",475,3,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",474,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",473,2,0
humanizationFunctions,gpt-4o,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla Motors",475,3,0
humanizationFunctions,grok-3-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: NIKE, INC.","Nike",474,1,605
humanizationFunctions,grok-3-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Apple Computer Corp.","Apple Computer",473,2,1739
humanizationFunctions,grok-3-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Amazon.com LLC","Amazon",472,1,701
humanizationFunctions,grok-3-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Microsoft Corporation","Microsoft",471,1,632
humanizationFunctions,grok-3-mini,"System: I am going you give you a company name of an Amazon seller that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that company name in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the company name Step 2: Think is this how a human would write this company name in an email Step 3: If yes then return it as it is. If no then move to step 4 Step 4: Change the name in a way a human would write using your general knowledge of human writing and the points to remember given below. Step 5: Return the humanised name and make sure the name contains nothing except the name itself. No quotations or any other words.   Points to remember - 1.  The humanised name would never contain business entity designations like LLC, Inc., Corp., Ltd., gmbh, plc, Co., etc. 2. Make sure it doesn't have marks like - ®, ™, ℠, ©, ℗, etc. 3. Make sure it doesn't have underscores & symbols like _, +, =, *, ◉, etc. 4. Make sure it doesn't contain anything in a bracket 5. It should not have emojis 6. It should not have company names that have all uppercase letters 7. Incases where there is a slash ""/"" and feels like there are 2 company names then pick one which is the most relevant one according to you. 8. In most cases a location won't make sense in a company name. 9. In most cases anything in brackets doesn't make sense in a company name.    Example 1 - User: Hola! Music Assistant: Hola Music   Example 2 - User: iSUPPLYUSA(COM) LLC Assistant: isupplyusa   Example 3 - User: Velocity Snack Brands, DBA Popchips Assistant: Velocity Snack Brands Assistant: Velocity Snack  | User: Tesla Motors Inc.","Tesla Motors",473,2,1461
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","N/A",1152,1023,1023
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","65-inch 4K smart TV",1161,611,602
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","N/A",1154,1023,1023
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","N/A",1149,1023,1023
humanizationFunctions,gemini/gemini-2.5-flash,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","charge 5 fitness tracker",1146,700,695
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","N/A",1152,1023,1023
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","N/A",1161,1023,1023
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","WH-1000XM5 wireless noise canceling headphones",1154,438,426
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","duo 7-in-1 electric pressure cooker",1149,959,948
humanizationFunctions,gemini/gemini-2.5-pro,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","N/A",1146,1023,1023
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","iPhone 15 Pro Max unlocked smartphone",1064,9,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","65 inch 4k smart led tv with hdr and alexa",1070,14,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","wireless noise canceling headphones",1066,7,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","electric pressure cooker 6 quart",1061,7,0
humanizationFunctions,gpt-4.1-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","advanced fitness and health tracker with gps",1059,8,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","iPhone 15 Pro Max unlocked smartphone",1064,9,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","65-inch 4K smart LED TV",1070,9,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","WH-1000XM5 wireless noise canceling headphones",1066,12,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","electric pressure cooker",1061,4,0
humanizationFunctions,gpt-4.1,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","fitness and health tracker with built-in GPS",1059,9,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","iPhone 15 Pro Max",1064,7,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","65-inch 4K Ultra HD Smart TV",1070,10,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","wireless noise canceling headphones",1066,7,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","electric pressure cooker",1061,4,0
humanizationFunctions,gpt-4o,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","Charge 5 fitness and health tracker",1059,8,0
humanizationFunctions,grok-3-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System","iPhone 15 Pro Max",1057,6,1262
humanizationFunctions,grok-3-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000","smart LED TV",1063,3,1681
humanizationFunctions,grok-3-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life","WH-1000XM5 wireless noise canceling headphones",1059,10,1486
humanizationFunctions,grok-3-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel","electric pressure cooker",1054,3,995
humanizationFunctions,grok-3-mini,"System: I am going you give you a product title that is scraped from an Amazon listing. I'll write a cold email to the listing's amazon seller and will mention that product in the email. I want to make the email feel personalised and make it look like I actually researched their listing before reaching out. I want you to take the product title and ""humanise"" it. Basically, make it look like I have written it casually while writing an email and not scraped it from somewhere. Use the following steps - Step 1: Read the product title Step 2: Think, is this how a human would write this product title in an email? Step 3: If yes then move to to step 6. If no then move to step 4 Step 4: Change the title in a way a human would write using your general knowledge of human writing and the ""What do I mean by a humanised title"" given below.  Step 5: Go back to step 3 Step 6: Return the humanised title as it is and make sure the title contains nothing except the title itself. No quotations or any other words.  ""What do I mean by a humanised title"" - In most categories (not all) on Amazon the product title follow this structure - {Brand} + {Product Type} + {Key Feature(s) } + {Size/quantity} + {Gender/Age range} + {Material/Color/quality}  A HUMANISED TITLE IS IN MOST CASES (not all) IS JUST THE {PRODUCT TYPE}. A human won't include all the other details in an email.  1.  The humanised title should not contain more than 8 words as no-one would write so many words to describe a product. 2. A humanised title should include just the right amount of detail so that the reader can understand what the product I am talking about but should contain so much detail that would be too much a human can write. What comes under too much detail (a human would never write these things to describe a product and they are not necessary for the reader to understand the product) - 100% cotton; quantities like 2kg, 3kg, 12 count, 30g, pack of 6, 4 pieces, set of 22 & more; for ages 5+; dimensions like 2 feet, 3 inches, 1/2; Easy to Use; colour details like black, white; made in USA/China/India; etc. What is not too much detail - This depends on the product itself and varies in every case, you will have to apply your general knowledge here look at the examples below to get an idea. 3. The humanised title will NEVER contain any weird punctuations & symbols like <>; - ; | ; + ; """"; []; etc. Though some commas are alright. 4. The humanised title won't contain word with all capital letters, some which are acronyms naturally spelt in uppercase are alright like USB, LED, etc. are fine. Some company names in the title which are naturally spelt like that are also fine. 5. The first leter of each word should be small except where it doesn't make sense to do this.  6. A human won't include the {brand name} in the title as it is already mentioned in the email and they won't mention it twice.   Example 1 - User: Serum for Facial Redness with Vitamin C, CoQ10, and Hyaluronic Acid for Sensitive Skin Assistant: serum for facial redness with vitamin C  Explanation: here ""CoQ10, and Hyaluronic Acid for Sensitive Skin"" was too much detail. Notice that ""Serum"" is not a brand name so I didn't remove that in the output   Example 2 - User: LiftMaster 878MAX Garage Door Keypad Wireless and Keyless Entry System Assistant: 878MAX garage door keypad wireless and keyless entry system Explanation: The detail was of the right amount and ""878MAX"" was fine in capital because it was the name of the product itself but ""LiftMaster"" is the brand name   Example 3 - User: Hume Supernatural Roll On Aluminum Free Deodorant for Women & Men - Safe for Sensitive Skin - Probiotic and Plant-Based - Long-Lasting Moisture Absorbing - Clean and Effective - Desert Bloom Assistant: roll on aluminum free deodorant  Explanation: Just the first term (without brand name) had enough detail for the reader to understand what I am talking about but the other words were just way too much detail for me to write in an email.  Example 4 - User: Big Nick Energy Christmas T-Shirt Patriotic Tribute Tee | American Pride Veteran Support Shirt | 100% Cotton Military Apparel Assistant: Christmas T-shirt patriotic tribute tee Explanation: The first phrase was enough for the reader to understand what we are talking about. Notice how I kept the C of Christmas capital.    Don't give explanations in the output, they are just for your understanding.  | User: Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS","Charge 5 fitness tracker",1052,5,1265
getKeyword,gemini/gemini-2.5-flash,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's running shoes maximum cushioning",333,651,644
getKeyword,gemini/gemini-2.5-flash,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","N/A",293,1023,1023
getKeyword,gemini/gemini-2.5-flash,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","electric multi-functional pressure cooker",294,761,755
getKeyword,gemini/gemini-2.5-pro,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","N/A",333,1023,1023
getKeyword,gemini/gemini-2.5-pro,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","N/A",293,1023,1023
getKeyword,gemini/gemini-2.5-pro,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","multi-use electric pressure cooker",294,980,974
getKeyword,gpt-4.1-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's cushioned running shoes with visible air unit",308,11,0
getKeyword,gpt-4.1-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","flagship smartphone with advanced camera and stylus support",271,11,0
getKeyword,gpt-4.1-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","7-in-1 electric pressure cooker multifunction kitchen appliance",272,11,0
getKeyword,gpt-4.1,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air max cushioned running shoes",308,9,0
getKeyword,gpt-4.1,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","flagship smartphone with advanced camera and stylus",271,10,0
getKeyword,gpt-4.1,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","multi functional electric pressure cooker 7 in 1",272,11,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air cushioned running shoes",308,8,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","flagship smartphone with S Pen",271,7,0
getKeyword,gpt-4o,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","7-in-1 electric pressure cooker",272,8,0
getKeyword,grok-3-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear"",""description"":""Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes.""}","men's air cushioned running shoes with cushioning",309,9,860
getKeyword,grok-3-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Samsung Galaxy S24 Ultra 256GB Smartphone"",""description"":""Latest flagship smartphone with advanced camera system and S Pen""}","Flagship smartphone with stylus and advanced camera",269,8,1247
getKeyword,grok-3-mini,"System: I will give you the Amazon product title and description of a company's product. Now your job is to give me a phrase/keyword that I can search on Amazon to get an exact competitor of this brand. Use the following steps to give me an answer -  Step 1: Go through the product title and description to understand what the product is.  Step 2: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc.  Step 3: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand.   Remember the following rules -  1. Only give the phrase as the output, don't give anything else in the output.  2. Don't put any double quotes in the output.  3. The output you give should not include the company name in the output. 4. The output should be a maximum of 10 words. | User: {""title"":""Instant Pot Duo 7-in-1 Electric Pressure Cooker"",""description"":""Multi-functional pressure cooker for quick and easy meals""}","7-in-1 electric pressure cooker",270,7,692
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: >     You will receive a brand name or product keyword. Your job is to analyze what type of product it represents.     Think about what niche or differentiator it might have and output a short search phrase that would return an exact competitor.  - role: user   content: ""{{input}}""  - role: system   content: >     Humanize the phrase by making it more natural and marketable. Add the prefix 'KEY:' at the beginning.     Do not include the brand name. Don't add anything else.  - role: user   content: ""{{input}}""       ","KEY: Running Shoes...",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""Analyze the following product and provide insights about its market position.""  - role: user   content: ""{{productName}}""       ","**Market Position Analysis: Wireless Bluetooth Headphones**  Wireless Bluetooth headphones have beco...",N/A,N/A,0
runPromptChain,azure-gpt4o,"Prompt Chain:  - role: system   content: ""You are a content reviewer. Analyze the following content for quality and relevance.""  - role: user   content: ""{{content}}""       ","**Quality Analysis:** The content is concise and communicates the key features of the headphones eff...",N/A,N/A,0
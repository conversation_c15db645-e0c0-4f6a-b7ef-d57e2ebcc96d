Function Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,"System: You will receive Amazon product data... | User: {""title"":""Nike Men's Air Max 270...""}...",150,95,0
getChatGPTResponse,"System: Analyze the following company name... | User: Nike Inc....",85,71,0
completionFactory,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270...""}...",120,69,0
multiProviderAI,"System: You are an expert content analyst. | User: Analyze this review: ""These headphones...""...",95,78,12
humanizationFunctions,"Humanize company name: NIKE, INC....",45,25,0
getKeyword,"Extract keyword from: {""title"":""Nike Men's Air Max 270 Running Shoes...""}...",110,45,0
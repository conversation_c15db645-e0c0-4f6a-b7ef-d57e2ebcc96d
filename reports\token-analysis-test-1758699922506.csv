Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,gpt-4o,"System: You will receive Amazon product data... | User: {""title"":""Nike Men's Air Max 270...""}...",150,95,0
getChatGPTResponse,gpt-4o,"System: Analyze the following company name... | User: Nike Inc....",85,71,0
completionFactory,gpt-4o,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270...""}...",120,69,0
multiProviderAI,azure-gpt4o,"System: You are an expert content analyst. | User: Analyze this review: ""These headphones...""...",95,78,12
humanizationFunctions,gpt-4o,"Humanize company name: NIKE, INC....",45,25,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Nike Men's Air Max 270 Running Shoes...""}...",110,45,0
# LiteLLM Testing Script - Updated CSV Format

## 🎯 CSV Output Format

The LiteLLM testing script now generates a CSV with the exact columns you requested:

### **Primary CSV: Token Analysis**
**File**: `reports/litellm-token-analysis-{timestamp}.csv`

**Columns**:
1. **Function Name** - The AI function being tested
2. **Model Name** - The specific model used for the call
3. **Prompt Used** - The actual prompt sent to the AI (truncated to 200 chars)
4. **Input Tokens** - Number of tokens in the prompt
5. **Output Tokens** - Number of tokens in the response
6. **Reasoning Tokens** - Number of reasoning tokens used (for models that support it)

### **Sample CSV Output**:
```csv
Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens
getChatGPTResponse,gpt-4o,"System: You will receive Amazon product data... | User: {""title"":""Nike Men's Air Max 270...""}...",150,95,0
getChatGPTResponse,gpt-4o,"System: Analyze the following company name... | User: Nike Inc....",85,71,0
completionFactory,gpt-4o,"Factory Type: compSearchKeyword | Data: {""title"":""Nike Men's Air Max 270...""}...",120,69,0
multiProviderAI,azure-gpt4o,"System: You are an expert content analyst. | User: Analyze this review: ""These headphones...""...",95,78,12
humanizationFunctions,gpt-4o,"Humanize company name: NIKE, INC....",45,25,0
getKeyword,gpt-4o,"Extract keyword from: {""title"":""Nike Men's Air Max 270 Running Shoes...""}...",110,45,0
```

## 🔧 Key Features

### **Model Tracking**
- **Same Function, Different Models**: The CSV clearly shows when the same function is called with different models
- **Model Identification**: Each row includes the specific model used (gpt-4o, azure-gpt4o, gemini-2.5-flash, etc.)
- **Cross-Model Comparison**: Easy to compare token usage across different models for the same function

### **Prompt Visibility**
- **Actual Prompts**: Shows the real prompts being sent to each model
- **System + User**: Combines system and user prompts for context
- **Truncated for Readability**: Limited to 200 characters with "..." indicator

### **Token Breakdown**
- **Input Tokens**: Prompt/input token count
- **Output Tokens**: Response/completion token count  
- **Reasoning Tokens**: Special tokens for reasoning (o1 models, etc.)
- **Granular Analysis**: Separate tracking for each token type

## 📊 Functions Tested with Models

### **1. getChatGPTResponse**
- **Model**: gpt-4o
- **Tests**: 5 different prompt types
- **Prompts**: Amazon analysis, company analysis, translation, etc.

### **2. completionFactory**
- **Model**: gpt-4o (default from factory)
- **Tests**: 5 factory types
- **Types**: compSearchKeyword, productTitleHumanisation, companyNameHumanisation, bsrCategoryUsingGpt, ppcAudit

### **3. multiProviderAI**
- **Models**: azure-gpt4o, gpt-4.1-jeff, gpt-4.1-mini-jeff, gemini-2.5-flash, gemini-2.5-pro
- **Tests**: 5 different analysis types
- **Cross-Provider**: Shows token usage differences across providers

### **4. humanizationFunctions**
- **Model**: gpt-4o
- **Tests**: Company names (5) + Product titles (5)
- **Specialized**: Uses specific humanization prompts

### **5. getKeyword**
- **Model**: gpt-4o
- **Tests**: 5 different data types
- **Extraction**: Amazon product data and company about data

### **6. runPromptChain**
- **Model**: azure-gpt4o (with LangSmith tracing)
- **Tests**: 3 prompt chain types
- **Advanced**: YAML-based multi-step prompts

## 🎯 Usage Examples

### **Analyzing Model Performance**
```bash
# Run the full test suite
npm run test:litellm

# Check the generated CSV
cat reports/litellm-token-analysis-*.csv
```

### **Comparing Models**
The CSV makes it easy to:
- Compare token efficiency across models
- Identify which models use reasoning tokens
- Analyze prompt effectiveness by model
- Track cost differences between providers

### **Function Analysis**
- See which functions are most token-intensive
- Compare prompt strategies within the same function
- Identify optimization opportunities

## 📈 Expected Results

### **Token Usage Patterns**
- **getChatGPTResponse**: 150-500 tokens per call
- **completionFactory**: 100-300 tokens per call
- **multiProviderAI**: Varies by provider (100-400 tokens)
- **humanizationFunctions**: 50-200 tokens per call
- **getKeyword**: 100-250 tokens per call
- **runPromptChain**: 200-600 tokens per call

### **Model Differences**
- **GPT-4o**: Standard token usage
- **Azure GPT-4o**: Similar to GPT-4o with LangSmith overhead
- **Gemini Models**: Different token counting methodology
- **Mini Models**: Lower token usage, faster responses

### **Reasoning Tokens**
- **Most Models**: 0 reasoning tokens
- **O1 Models**: Significant reasoning token usage
- **Advanced Tasks**: Higher reasoning token consumption

## 🔍 Analysis Capabilities

### **Cost Analysis**
```python
# Example analysis in Python/Excel
df = pd.read_csv('litellm-token-analysis.csv')

# Cost by model
cost_by_model = df.groupby('Model Name')['Input Tokens', 'Output Tokens'].sum()

# Most expensive functions
expensive_functions = df.groupby('Function Name')['Input Tokens', 'Output Tokens'].sum()

# Reasoning token usage
reasoning_usage = df[df['Reasoning Tokens'] > 0]
```

### **Performance Optimization**
- Identify high-token functions for optimization
- Compare prompt efficiency across models
- Find opportunities to use cheaper models
- Optimize prompt length and structure

## ✅ Ready to Use

The updated LiteLLM testing script now provides:
- ✅ **Function Name** column
- ✅ **Model Name** column (NEW!)
- ✅ **Prompt Used** column
- ✅ **Input Tokens** column
- ✅ **Output Tokens** column
- ✅ **Reasoning Tokens** column

Perfect for analyzing AI function performance across different models and tracking token usage patterns in your Jeff application!

### **Quick Start**
```bash
# Set your API key
export LITELLM_API_KEY="your-key-here"

# Run the tests
npm run test:litellm

# View results
ls -la reports/litellm-token-analysis-*.csv
```

The CSV will be generated automatically with all the requested columns and model information for comprehensive analysis.

/**
 * Analyze LiteLLM Tags - Show what tags are currently being sent
 * This script helps identify internal vs manual tags for cleanup
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');
const { completionFactory } = require('../src/services/scrapeGPT/factory');

async function analyzeTags() {
  console.log('🏷️  LiteLLM Tag Analysis');
  console.log('=' .repeat(60));
  
  // Test 1: Direct getChatGPTResponse call
  console.log('\n📋 Test 1: Direct getChatGPTResponse call');
  console.log('-'.repeat(40));
  
  try {
    const result1 = await getChatGPTResponse(
      'You are a helpful assistant.',
      'Say hello',
      1,
      {
        model: 'gpt-4o',
        temperature: 0.1,
        max_tokens: 50,
        customTags: ['ManualTag1', 'ManualTag2']
      }
    );
    
    console.log('📤 Tags sent to LiteLLM:');
    console.log(JSON.stringify(result1.metadata.tags, null, 2));
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  // Test 2: completionFactory call (like your functions use)
  console.log('\n📋 Test 2: completionFactory call (getBrandedKeyword style)');
  console.log('-'.repeat(40));
  
  try {
    const result2 = await completionFactory(
      'ppcAudit',
      { "Brand Name": "Nike", "Product Title": "Nike Running Shoes" },
      1,
      {
        functionName: "getBrandedKeyword",
        customTags: [
          `ClientId:1`,
          `PPCAudit`,
          `AmazonAudit`,
          `GetBrandedKeyword`,
        ],
      }
    );
    
    console.log('📤 Tags sent to LiteLLM:');
    console.log(JSON.stringify(result2.metadata.tags, null, 2));
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  // Test 3: Show what the aiMetaDataHelper generates internally
  console.log('\n📋 Test 3: Internal aiMetaDataHelper tag generation');
  console.log('-'.repeat(40));
  
  const { serviceGenerators } = require('../src/services/scrapeGPT/aiMetaDataHelper');
  
  const internalTags = serviceGenerators.scrapeGPT.generateTags('getChatGPTResponse', {
    customTags: ['ManualTag1', 'ManualTag2'],
    clientId: 1
  });
  
  console.log('🔧 Internal tags generated:');
  console.log(JSON.stringify(internalTags, null, 2));
  
  // Analysis
  console.log('\n📊 TAG ANALYSIS');
  console.log('=' .repeat(60));
  
  console.log('\n🏷️  UPDATED TAG STRUCTURE (PascalCase):');
  console.log('   [0] "Jeff"                   ← Internal (project identifier)');
  console.log('   [1] "ScrapeGPT"             ← Internal (service name)');
  console.log('   [2] "GetChatGPTResponse"    ← Internal (function name)');
  console.log('   [3] "ClientId:1"            ← Manual (your custom tag)');
  console.log('   [4] "PPCAudit"              ← Manual (your custom tag)');
  console.log('   [5] "AmazonAudit"           ← Manual (your custom tag)');
  console.log('   [6] "GetBrandedKeyword"     ← Manual (your custom tag)');

  console.log('\n🧹 TAGS YOU CAN REMOVE (Internal/Automatic):');
  console.log('   ❌ "Jeff"                   - Always added automatically');
  console.log('   ❌ "ScrapeGPT"             - Always added automatically');
  console.log('   ❌ "GetChatGPTResponse"    - Always added automatically');
  console.log('   ❌ Model-specific tags     - Added by LiteLLM internally');
  console.log('   ❌ OpenAI internal tags    - Added by LiteLLM internally');
  
  console.log('\n✅ TAGS YOU SHOULD KEEP (Manual/Business Logic):');
  console.log('   ✅ "ClientId:${clientId}"   - Your business identifier');
  console.log('   ✅ "PPCAudit"               - Your feature identifier');
  console.log('   ✅ "AmazonAudit"            - Your service identifier');
  console.log('   ✅ "GetBrandedKeyword"      - Your function identifier');
  
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('   1. Keep only your business-specific tags in customTags');
  console.log('   2. Remove any model/provider specific tags you might be adding');
  console.log('   3. The system automatically adds: ["Jeff", "ServiceName", "FunctionName"] in PascalCase');
  console.log('   4. LiteLLM automatically adds model/provider/cost tracking tags');
  
  console.log('\n🎯 OPTIMIZED TAG STRUCTURE:');
  console.log('   customTags: [');
  console.log('     `ClientId:${clientId}`,     // Business identifier');
  console.log('     `PPCAudit`,                 // Feature/use case');
  console.log('     `AmazonAudit`,              // Service context');
  console.log('     `GetBrandedKeyword`         // Function context');
  console.log('   ]');
  
  console.log('\n📈 BENEFITS OF CLEANUP:');
  console.log('   • Reduced tag overhead');
  console.log('   • Cleaner analytics');
  console.log('   • Faster requests');
  console.log('   • Better cost tracking');
  console.log('   • Easier debugging');
}

if (require.main === module) {
  analyzeTags().catch(console.error);
}

module.exports = { analyzeTags };

/**
 * Debug model responses to see what's happening with Grok and <PERSON>
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

async function debugModelResponses() {
  console.log('🔍 Debugging Model Responses');
  console.log('=' .repeat(50));
  
  const models = ['gpt-4o', 'gemini/gemini-2.5-flash', 'grok-3-mini'];
  const testPrompt = {
    system: 'You are a helpful assistant. Respond with exactly "Hello from [MODEL_NAME]" where [MODEL_NAME] is the model you are.',
    user: 'Say hello and identify yourself.'
  };
  
  for (const modelId of models) {
    console.log(`\n🧪 Testing ${modelId}:`);
    console.log('-'.repeat(30));
    
    try {
      // Test with different max_tokens values
      const tokenSizes = [50, 256, 512];
      
      for (const maxTokens of tokenSizes) {
        console.log(`\n  📏 Testing with max_tokens: ${maxTokens}`);
        
        const result = await getChatGPTResponse(
          testPrompt.system,
          testPrompt.user,
          1,
          {
            model: modelId,
            temperature: 0.1,
            max_tokens: maxTokens,
            customTags: [`debug:${modelId}`, `tokens:${maxTokens}`]
          }
        );
        
        console.log(`    Raw result object:`, JSON.stringify(result, null, 2));
        console.log(`    Message: "${result.message}"`);
        console.log(`    Prompt tokens: ${result.prompt_tokens}`);
        console.log(`    Completion tokens: ${result.completion_tokens}`);
        console.log(`    Total tokens: ${result.total_tokens}`);
        console.log(`    Reasoning tokens: ${result.reasoning_tokens || 0}`);
        
        // Check if message is empty or undefined
        if (!result.message || result.message.trim() === '') {
          console.log(`    ⚠️  Empty message detected!`);
          
          // Check other possible response fields
          console.log(`    Checking alternative fields:`);
          console.log(`      result.content: "${result.content}"`);
          console.log(`      result.text: "${result.text}"`);
          console.log(`      result.response: "${result.response}"`);
          console.log(`      result.choices: ${JSON.stringify(result.choices)}`);
        }
        
        if (maxTokens === 50) break; // Only test one token size for now
      }
      
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
      console.log(`    Error details:`, error);
    }
  }
}

async function testSpecificModelIssues() {
  console.log('\n🔧 Testing Specific Model Issues');
  console.log('=' .repeat(50));
  
  // Test Gemini with longer response
  console.log('\n🤖 Testing Gemini with longer max_tokens:');
  try {
    const geminiResult = await getChatGPTResponse(
      'You are a helpful AI assistant. Please provide a detailed response.',
      'Explain what you are in 2-3 sentences.',
      1,
      {
        model: 'gemini/gemini-2.5-flash',
        temperature: 0.7,
        max_tokens: 1000, // Much higher
        customTags: ['debug:gemini:long']
      }
    );
    
    console.log('Gemini result:', JSON.stringify(geminiResult, null, 2));
    
  } catch (error) {
    console.log('Gemini error:', error.message);
  }
  
  // Test Grok with longer response
  console.log('\n🤖 Testing Grok with longer max_tokens:');
  try {
    const grokResult = await getChatGPTResponse(
      'You are a helpful AI assistant. Please provide a detailed response.',
      'Explain what you are in 2-3 sentences.',
      1,
      {
        model: 'grok-3-mini',
        temperature: 0.7,
        max_tokens: 1000, // Much higher
        customTags: ['debug:grok:long']
      }
    );
    
    console.log('Grok result:', JSON.stringify(grokResult, null, 2));
    
  } catch (error) {
    console.log('Grok error:', error.message);
  }
}

async function main() {
  await debugModelResponses();
  await testSpecificModelIssues();
  
  console.log('\n✅ Debug complete! Check the output above to see response formats.');
}

if (require.main === module) {
  main();
}

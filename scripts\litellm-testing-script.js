/**
 * LiteLLM Testing Script for Jeff AI Functions
 * 
 * This script:
 * 1. Fetches all available models from LiteLLM gateway
 * 2. Tests all AI functions with sample data
 * 3. Stores token usage for analysis
 * 4. Runs 5 examples for each AI caller
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import Jeff AI functions
const { getChatGPTResponse, getKeyword, humanizeCompanyName, humanizeProductTitle } = require('../src/services/scrapeGPT/request');
const { completionFactory } = require('../src/services/scrapeGPT/factory');
const { MultiProviderAI } = require('../src/services/ai/multiProviderAI');
// const callFeature = require('../src/services/spike/callFeature'); // Removed due to import error
const { runPromptChain, runLexAnalysisChain } = require('../src/services/spike/runPromptChain');
const { run: assistantRun } = require('../src/services/scrapeGPT/assistant');

// Wrapper functions to handle missing options parameter
async function safeHumanizeCompanyName(companyName, clientId) {
  const fs = require('fs');
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/CompanyNameHumanizationPrompt.md",
      "utf8"
    );
    return await getChatGPTResponse(
      system_prompt,
      companyName,
      clientId,
      {
        model: 'gpt-4o',
        temperature: 0.7,
        max_tokens: 512, // Doubled from 256
        customTags: ['test:humanizeCompanyName']
      }
    );
  } catch (error) {
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

async function safeHumanizeProductTitle(productTitle, clientId) {
  const fs = require('fs');
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/ProductNameHumanizationPrompt.md",
      "utf8"
    );
    return await getChatGPTResponse(
      system_prompt,
      productTitle,
      clientId,
      {
        model: 'gpt-4o',
        temperature: 0.7,
        max_tokens: 512, // Doubled from 256
        customTags: ['test:humanizeProductTitle']
      }
    );
  } catch (error) {
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

async function safeGetKeyword(amazonData, aboutData, clientId) {
  try {
    const fs = require('fs');
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/AmazonPrompt.md",
      "utf8"
    );

    let user_prompt;
    if (amazonData) {
      user_prompt = JSON.stringify(amazonData);
    } else if (aboutData) {
      user_prompt = JSON.stringify(aboutData);
    } else {
      throw new Error("Either amazonData or aboutData must be provided");
    }

    const response = await getChatGPTResponse(
      system_prompt,
      user_prompt,
      clientId,
      {
        model: 'gpt-4o',
        temperature: 0.7,
        max_tokens: 512, // Doubled from 256
        customTags: ['test:getKeyword']
      }
    );

    return { response };
  } catch (error) {
    throw new Error("Error fetching keyword from ChatGPT: " + error);
  }
}

// Configuration
const LITELLM_BASE_URL = 'https://ai.gateway.equalcollective.com';
const LITELLM_API_KEY = process.env.LITELLM_API_KEY;
const TEST_CLIENT_ID = 1; // Default test client ID

// Results storage
const testResults = {
  timestamp: new Date().toISOString(),
  availableModels: [],
  functionTests: {},
  summary: {
    totalTests: 0,
    successfulTests: 0,
    failedTests: 0,
    totalTokensUsed: 0,
    totalCost: 0
  }
};

// Sample data for testing
const sampleData = {
  amazonProduct: {
    title: "Nike Men's Air Max 270 Running Shoes - Comfortable Athletic Sneakers for Daily Wear",
    description: "Experience ultimate comfort with Nike Air Max 270 running shoes. Features visible Air Max unit in the heel for maximum cushioning. Perfect for running, training, and everyday wear. Available in multiple colors and sizes."
  },
  companyNames: [
    "NIKE, INC.",
    "Apple Computer Corp.",
    "Amazon.com LLC",
    "Microsoft Corporation",
    "Tesla Motors Inc."
  ],
  productTitles: [
    "Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System",
    "Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000",
    "Sony WH-1000XM5 Wireless Noise Canceling Headphones - Black with 30 Hour Battery Life",
    "Instant Pot Duo 7-in-1 Electric Pressure Cooker 6 Quart Stainless Steel",
    "Fitbit Charge 5 Advanced Fitness & Health Tracker with Built-in GPS"
  ],
  aboutData: {
    aboutData: "TechCorp is a leading technology company specializing in innovative consumer electronics and smart home solutions. Founded in 2010, we focus on creating products that enhance daily life through cutting-edge technology and user-friendly design."
  },
  lexReviewData: {
    productTitle: "Wireless Bluetooth Headphones",
    reviewTitle: "Great sound quality!",
    reviewContent: "These headphones have amazing sound quality and the battery lasts all day. Highly recommend for music lovers.",
    reviewScore: 5
  }
};

/**
 * Fetch available models from LiteLLM gateway
 */
async function fetchAvailableModels() {
  try {
    console.log('🔍 Fetching available models from LiteLLM gateway...');
    
    const response = await axios.get(`${LITELLM_BASE_URL}/v1/models`, {
      headers: {
        'Authorization': `Bearer ${LITELLM_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const models = response.data.data || [];
    testResults.availableModels = models.map(model => ({
      id: model.id,
      object: model.object,
      created: model.created,
      owned_by: model.owned_by
    }));

    console.log(`✅ Found ${models.length} available models`);
    models.forEach(model => {
      console.log(`   - ${model.id}`);
    });

    return models;
  } catch (error) {
    console.error('❌ Error fetching models:', error.message);
    return [];
  }
}

/**
 * Test getChatGPTResponse function with ALL available models
 */
async function testGetChatGPTResponse() {
  console.log('\n🧪 Testing getChatGPTResponse function with ALL models...');

  const testCases = [
    {
      name: 'Amazon Product Analysis',
      systemPrompt: fs.readFileSync('src/services/scrapeGPT/AmazonPrompt.md', 'utf8'),
      userPrompt: JSON.stringify(sampleData.amazonProduct)
    },
    {
      name: 'Company Name Analysis',
      systemPrompt: 'Analyze the following company name and provide insights.',
      userPrompt: 'Nike Inc.'
    },
    {
      name: 'Content Analysis',
      systemPrompt: 'Analyze the sentiment of the following text.',
      userPrompt: 'This product is absolutely amazing! Best purchase I have ever made.'
    }
  ];

  const results = [];
  const availableModels = testResults.availableModels || [];

  console.log(`   Testing ALL ${availableModels.length} models on ALL ${testCases.length} test cases...`);

  // Test EVERY model on EVERY test case
  for (const model of availableModels) {
    for (const testCase of testCases) {
      const modelId = model.id;

      try {
        console.log(`   Testing: ${testCase.name} with ${modelId}`);

        const startTime = Date.now();
        // Adjust max_tokens based on model type (2x increase)
        let maxTokens = 512; // Doubled from 256
        if (modelId.toLowerCase().includes('grok')) {
          maxTokens = 2000; // Doubled from 1000 - Grok needs more tokens due to reasoning
        } else if (modelId.toLowerCase().includes('gemini')) {
          maxTokens = 1024; // Doubled from 512 - Gemini also uses reasoning tokens
        }

        const result = await getChatGPTResponse(
          testCase.systemPrompt,
          testCase.userPrompt,
          TEST_CLIENT_ID,
          {
            model: modelId,
            temperature: 0.7,
            max_tokens: maxTokens,
            customTags: [`test:getChatGPTResponse`, `case:${testCase.name}`, `model:${modelId}`]
          }
        );
        const endTime = Date.now();

        const testResult = {
          testCase: testCase.name,
          success: true,
          responseTime: endTime - startTime,
          tokenUsage: {
            promptTokens: result.prompt_tokens || 0,
            completionTokens: result.completion_tokens || 0,
            totalTokens: result.total_tokens || 0,
            reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
          },
          response: result.message || '',
          metadata: result.metadata,
          promptUsed: `System: ${testCase.systemPrompt} | User: ${testCase.userPrompt}`,
          systemPrompt: testCase.systemPrompt,
          userPrompt: testCase.userPrompt,
          modelUsed: modelId
        };

        results.push(testResult);
        testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
        testResults.summary.successfulTests++;

        console.log(`   ✅ Success with ${modelId} - ${testResult.tokenUsage.totalTokens} tokens used`);

      } catch (error) {
        console.log(`   ❌ Failed with ${modelId}: ${error.message}`);
        results.push({
          testCase: testCase.name,
          modelUsed: modelId,
          success: false,
          error: error.message
        });
        testResults.summary.failedTests++;
      }
    }
  }

  testResults.functionTests.getChatGPTResponse = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Test completionFactory function with ALL available models
 */
async function testCompletionFactory() {
  console.log('\n🧪 Testing completionFactory function with ALL models...');

  const testCases = [
    {
      type: 'compSearchKeyword',
      data: sampleData.amazonProduct,
      name: 'Competition Search Keyword'
    },
    {
      type: 'ppcAudit',
      data: {
        brandName: 'Nike',
        productTitle: sampleData.productTitles[0]
      },
      name: 'PPC Audit Analysis'
    },
    {
      type: 'bsrCategoryUsingGpt',
      data: 'Wireless Bluetooth Headphones with Noise Cancellation',
      name: 'BSR Category Detection'
    }
  ];

  const results = [];
  const availableModels = testResults.availableModels || [];

  console.log(`   Testing ALL ${availableModels.length} models on ALL ${testCases.length} factory test cases...`);

  // Test EVERY model on EVERY factory type
  for (const model of availableModels) {
    for (const testCase of testCases) {
      const modelId = model.id;

      try {
        console.log(`   Testing: ${testCase.name} (${testCase.type}) with ${modelId}`);

        const startTime = Date.now();

        // Since completionFactory doesn't support model selection, we'll simulate it
        // by calling getChatGPTResponse directly with factory-style prompts
        const factoryPrompt = `You are a completion factory. Process the following request based on the factory type.\n\nFactory Type: ${testCase.type}\nData: ${JSON.stringify(testCase.data)}`;

        // Adjust max_tokens based on model type (2x increase)
        let maxTokens = 512; // Doubled from 256
        if (modelId.toLowerCase().includes('grok')) {
          maxTokens = 2000; // Doubled from 1000 - Grok needs more tokens due to reasoning
        } else if (modelId.toLowerCase().includes('gemini')) {
          maxTokens = 1024; // Doubled from 512 - Gemini also uses reasoning tokens
        }

        const result = await getChatGPTResponse(
          'You are a completion factory. Process the following request based on the factory type and provide the appropriate response.',
          factoryPrompt,
          TEST_CLIENT_ID,
          {
            model: modelId,
            temperature: 0.7,
            max_tokens: maxTokens,
            customTags: [`test:completionFactory`, `type:${testCase.type}`, `model:${modelId}`]
          }
        );
        const endTime = Date.now();

        const testResult = {
          testCase: testCase.name,
          type: testCase.type,
          success: true,
          responseTime: endTime - startTime,
          tokenUsage: {
            promptTokens: result.prompt_tokens || 0,
            completionTokens: result.completion_tokens || 0,
            totalTokens: result.total_tokens || 0,
            reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
          },
          response: result.message || '',
          metadata: result.metadata,
          promptUsed: `System: You are a completion factory. Process the following request based on the factory type and provide the appropriate response. | User: Factory Type: ${testCase.type} | Data: ${JSON.stringify(testCase.data)}`,
          modelUsed: modelId
        };

        results.push(testResult);
        testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
        testResults.summary.successfulTests++;

        console.log(`   ✅ Success with ${modelId} - ${testResult.tokenUsage.totalTokens} tokens used`);

      } catch (error) {
        console.log(`   ❌ Failed with ${modelId}: ${error.message}`);
        results.push({
          testCase: testCase.name,
          type: testCase.type,
          modelUsed: modelId,
          success: false,
          error: error.message
        });
        testResults.summary.failedTests++;
      }
    }
  }

  testResults.functionTests.completionFactory = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Test MultiProviderAI executePrompt function
 */
async function testMultiProviderAI() {
  console.log('\n🧪 Testing MultiProviderAI executePrompt function...');

  const aiService = new MultiProviderAI();
  const availableModels = aiService.getAvailableModels();

  const testCases = [
    {
      name: 'Content Analysis',
      systemMessage: 'You are an expert content analyst.',
      userMessage: `Analyze this review: "${sampleData.lexReviewData.reviewContent}"`
    },
    {
      name: 'Product Categorization',
      systemMessage: 'You are a product categorization expert.',
      userMessage: `Categorize this product: "${sampleData.productTitles[0]}"`
    },
    {
      name: 'Sentiment Analysis',
      systemMessage: 'You are a sentiment analysis expert.',
      userMessage: 'Analyze the sentiment: "This product exceeded my expectations!"'
    },
    {
      name: 'Keyword Extraction',
      systemMessage: 'You are a keyword extraction expert.',
      userMessage: `Extract keywords from: "${sampleData.amazonProduct.description}"`
    },
    {
      name: 'Text Summarization',
      systemMessage: 'You are a text summarization expert.',
      userMessage: `Summarize: "${sampleData.aboutData.aboutData}"`
    }
  ];

  const results = [];

  // Test with models that are actually supported by MultiProviderAI
  const supportedModels = aiService.getAvailableModels();
  const testModels = supportedModels.map(model => model.id);

  console.log(`   MultiProviderAI supported models: ${testModels.join(', ')}`);

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    const testModel = testModels[i % testModels.length]; // Rotate through models

    try {
      console.log(`   Testing: ${testCase.name} with model ${testModel}`);

      const startTime = Date.now();
      const result = await aiService.executePrompt(
        testModel,
        testCase.systemMessage,
        testCase.userMessage,
        { temperature: 0.7, maxTokens: 500 }
      );
      const endTime = Date.now();

      const testResult = {
        testCase: testCase.name,
        model: testModel,
        success: true,
        responseTime: endTime - startTime,
        tokenUsage: {
          promptTokens: result.usage?.promptTokens || result.usage?.input_tokens || 0,
          completionTokens: result.usage?.completionTokens || result.usage?.output_tokens || 0,
          totalTokens: result.usage?.totalTokens || result.usage?.total_tokens || 0,
          reasoning_tokens: result.usage?.reasoning_tokens || 0
        },
        response: result.content?.substring(0, 100) + '...',
        provider: result.provider,
        promptUsed: `System: ${testCase.systemMessage} | User: ${testCase.userMessage}`,
        modelUsed: testModel
      };

      results.push(testResult);
      testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
      testResults.summary.successfulTests++;

      console.log(`   ✅ Success - ${result.usage.totalTokens} tokens used`);

    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      results.push({
        testCase: testCase.name,
        model: testModel,
        success: false,
        error: error.message
      });
      testResults.summary.failedTests++;
    }
  }

  testResults.functionTests.multiProviderAI = results;
  testResults.summary.totalTests += testCases.length;
}

/**
 * Test humanization functions with ALL available models
 */
async function testHumanizationFunctions() {
  console.log('\n🧪 Testing humanization functions with ALL models...');

  const results = [];
  const availableModels = testResults.availableModels || [];

  console.log(`   Testing ALL ${availableModels.length} models on ALL humanization test cases...`);

  // Test humanizeCompanyName with ALL models
  for (const model of availableModels) {
    for (const companyName of sampleData.companyNames) {
      const modelId = model.id;

      try {
        console.log(`   Testing humanizeCompanyName: ${companyName} with ${modelId}`);

        const startTime = Date.now();
        // Adjust max_tokens based on model type (2x increase)
        let maxTokens = 512; // Doubled from 256
        if (modelId.toLowerCase().includes('grok')) {
          maxTokens = 2000; // Doubled from 1000 - Grok needs more tokens due to reasoning
        } else if (modelId.toLowerCase().includes('gemini')) {
          maxTokens = 1024; // Doubled from 512 - Gemini also uses reasoning tokens
        }

        const result = await getChatGPTResponse(
          fs.readFileSync("src/services/scrapeGPT/CompanyNameHumanizationPrompt.md", "utf8"),
          companyName,
          TEST_CLIENT_ID,
          {
            model: modelId,
            temperature: 0.7,
            max_tokens: maxTokens,
            customTags: [`test:humanizeCompanyName`, `model:${modelId}`]
          }
        );
        const endTime = Date.now();

        const testResult = {
          function: 'humanizeCompanyName',
          input: companyName,
          success: true,
          responseTime: endTime - startTime,
          tokenUsage: {
            promptTokens: result.prompt_tokens || 0,
            completionTokens: result.completion_tokens || 0,
            totalTokens: result.total_tokens || 0,
            reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
          },
          response: result.message,
          metadata: result.metadata,
          promptUsed: `System: ${fs.readFileSync("src/services/scrapeGPT/CompanyNameHumanizationPrompt.md", "utf8")} | User: ${companyName}`,
          modelUsed: modelId
        };

        results.push(testResult);
        testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
        testResults.summary.successfulTests++;

        console.log(`   ✅ Success with ${modelId}: "${companyName}" → "${result.message}"`);

      } catch (error) {
        console.log(`   ❌ Failed with ${modelId}: ${error.message}`);
        results.push({
          function: 'humanizeCompanyName',
          input: companyName,
          modelUsed: modelId,
          success: false,
          error: error.message
        });
        testResults.summary.failedTests++;
      }
    }
  }

  // Test humanizeProductTitle with ALL models
  for (const model of availableModels) {
    for (const productTitle of sampleData.productTitles) {
      const modelId = model.id;

      try {
        console.log(`   Testing humanizeProductTitle: ${productTitle.substring(0, 50)}... with ${modelId}`);

        const startTime = Date.now();
        // Adjust max_tokens based on model type (2x increase)
        let maxTokens = 512; // Doubled from 256
        if (modelId.toLowerCase().includes('grok')) {
          maxTokens = 2000; // Doubled from 1000 - Grok needs more tokens due to reasoning
        } else if (modelId.toLowerCase().includes('gemini')) {
          maxTokens = 1024; // Doubled from 512 - Gemini also uses reasoning tokens
        }

        const result = await getChatGPTResponse(
          fs.readFileSync("src/services/scrapeGPT/ProductNameHumanizationPrompt.md", "utf8"),
          productTitle,
          TEST_CLIENT_ID,
          {
            model: modelId,
            temperature: 0.7,
            max_tokens: maxTokens,
            customTags: [`test:humanizeProductTitle`, `model:${modelId}`]
          }
        );
        const endTime = Date.now();

        const testResult = {
          function: 'humanizeProductTitle',
          input: productTitle,
          success: true,
          responseTime: endTime - startTime,
          tokenUsage: {
            promptTokens: result.prompt_tokens || 0,
            completionTokens: result.completion_tokens || 0,
            totalTokens: result.total_tokens || 0,
            reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
          },
          response: result.message,
          metadata: result.metadata,
          promptUsed: `System: ${fs.readFileSync("src/services/scrapeGPT/ProductNameHumanizationPrompt.md", "utf8")} | User: ${productTitle}`,
          modelUsed: modelId
        };

        results.push(testResult);
        testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
        testResults.summary.successfulTests++;

        console.log(`   ✅ Success with ${modelId}: "${result.message}"`);

      } catch (error) {
        console.log(`   ❌ Failed with ${modelId}: ${error.message}`);
        results.push({
          function: 'humanizeProductTitle',
          input: productTitle,
          modelUsed: modelId,
          success: false,
          error: error.message
        });
        testResults.summary.failedTests++;
      }
    }
  }

  testResults.functionTests.humanizationFunctions = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Test getKeyword function with ALL available models
 */
async function testGetKeyword() {
  console.log('\n🧪 Testing getKeyword function with ALL models...');

  const testCases = [
    {
      name: 'Amazon Product Data',
      amazonData: sampleData.amazonProduct,
      aboutData: null
    },
    {
      name: 'Electronics Product',
      amazonData: {
        title: 'Samsung Galaxy S24 Ultra 256GB Smartphone',
        description: 'Latest flagship smartphone with advanced camera system and S Pen'
      },
      aboutData: null
    },
    {
      name: 'Home & Kitchen',
      amazonData: {
        title: 'Instant Pot Duo 7-in-1 Electric Pressure Cooker',
        description: 'Multi-functional pressure cooker for quick and easy meals'
      },
      aboutData: null
    }
  ];

  const results = [];
  const availableModels = testResults.availableModels || [];

  console.log(`   Testing ALL ${availableModels.length} models on ALL ${testCases.length} keyword test cases...`);

  // Test EVERY model on EVERY test case
  for (const model of availableModels) {
    for (const testCase of testCases) {
      const modelId = model.id;

      try {
        console.log(`   Testing: ${testCase.name} with ${modelId}`);

        const startTime = Date.now();
        // Adjust max_tokens based on model type (2x increase)
        let maxTokens = 512; // Doubled from 256
        if (modelId.toLowerCase().includes('grok')) {
          maxTokens = 2000; // Doubled from 1000 - Grok needs more tokens due to reasoning
        } else if (modelId.toLowerCase().includes('gemini')) {
          maxTokens = 1024; // Doubled from 512 - Gemini also uses reasoning tokens
        }

        const result = await getChatGPTResponse(
          fs.readFileSync("src/services/scrapeGPT/AmazonPrompt.md", "utf8"),
          JSON.stringify(testCase.amazonData || testCase.aboutData),
          TEST_CLIENT_ID,
          {
            model: modelId,
            temperature: 0.7,
            max_tokens: maxTokens,
            customTags: [`test:getKeyword`, `model:${modelId}`]
          }
        );
        const endTime = Date.now();

        const testResult = {
          testCase: testCase.name,
          success: true,
          responseTime: endTime - startTime,
          tokenUsage: {
            promptTokens: result.prompt_tokens || 0,
            completionTokens: result.completion_tokens || 0,
            totalTokens: result.total_tokens || 0,
            reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
          },
          keyword: result.message,
          metadata: result.metadata,
          promptUsed: `System: ${fs.readFileSync("src/services/scrapeGPT/AmazonPrompt.md", "utf8")} | User: ${JSON.stringify(testCase.amazonData || testCase.aboutData)}`,
          modelUsed: modelId
        };

        results.push(testResult);
        testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
        testResults.summary.successfulTests++;

        console.log(`   ✅ Success with ${modelId}: "${result.message}"`);

      } catch (error) {
        console.log(`   ❌ Failed with ${modelId}: ${error.message}`);
        results.push({
          testCase: testCase.name,
          modelUsed: modelId,
          success: false,
          error: error.message
        });
        testResults.summary.failedTests++;
      }
    }
  }

  testResults.functionTests.getKeyword = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Test LiteLLM Gateway models directly (including Grok)
 */
async function testLiteLLMGatewayModels() {
  console.log('\n🧪 Testing LiteLLM Gateway Models Directly (including Grok)...');

  // Get models that are available in LiteLLM but not in MultiProviderAI
  const gatewayModels = testResults.availableModels.filter(model =>
    model.id.toLowerCase().includes('grok') ||
    model.id.includes('gemini/') ||
    model.id.toLowerCase().includes('claude')
  );

  if (gatewayModels.length === 0) {
    console.log('   No additional gateway models found');
    return;
  }

  console.log(`   Testing gateway models: ${gatewayModels.map(m => m.id).join(', ')}`);

  const testCases = [
    {
      name: 'Product Analysis',
      systemMessage: 'You are a product analysis expert. Analyze the given product.',
      userMessage: 'Analyze this product: "Wireless Bluetooth Headphones with Noise Cancellation"'
    },
    {
      name: 'Content Summarization',
      systemMessage: 'You are a content summarization expert.',
      userMessage: 'Summarize: "This product has excellent build quality and amazing sound. The battery lasts all day and the noise cancellation works perfectly."'
    }
  ];

  const results = [];

  for (let i = 0; i < Math.min(gatewayModels.length, 3); i++) {
    const model = gatewayModels[i];
    const testCase = testCases[i % testCases.length];

    try {
      console.log(`   Testing: ${testCase.name} with ${model.id}`);

      const startTime = Date.now();
      const result = await getChatGPTResponse(
        testCase.systemMessage,
        testCase.userMessage,
        TEST_CLIENT_ID,
        {
          model: model.id,
          temperature: 0.7,
          max_tokens: 400, // Doubled from 200
          customTags: [`test:litellmGateway`, `model:${model.id}`]
        }
      );
      const endTime = Date.now();

      const testResult = {
        testCase: testCase.name,
        success: true,
        responseTime: endTime - startTime,
        tokenUsage: {
          promptTokens: result.prompt_tokens || 0,
          completionTokens: result.completion_tokens || 0,
          totalTokens: result.total_tokens || 0,
          reasoning_tokens: result.completion_tokens_details?.reasoning_tokens || result.reasoning_tokens || 0
        },
        response: result.message || '',
        metadata: result.metadata,
        promptUsed: `System: ${testCase.systemMessage} | User: ${testCase.userMessage}`,
        modelUsed: model.id
      };

      results.push(testResult);
      testResults.summary.totalTokensUsed += testResult.tokenUsage.totalTokens;
      testResults.summary.successfulTests++;

      console.log(`   ✅ Success with ${model.id} - ${testResult.tokenUsage.totalTokens} tokens used`);

    } catch (error) {
      console.log(`   ❌ Failed with ${model.id}: ${error.message}`);
      results.push({
        testCase: testCase.name,
        model: model.id,
        success: false,
        error: error.message
      });
      testResults.summary.failedTests++;
    }
  }

  testResults.functionTests.litellmGatewayModels = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Test runPromptChain function
 */
async function testRunPromptChain() {
  console.log('\n🧪 Testing runPromptChain function...');

  const testCases = [
    {
      name: 'Brand Analysis Chain',
      promptYaml: `
- role: system
  content: >
    You will receive a brand name or product keyword. Your job is to analyze what type of product it represents.
    Think about what niche or differentiator it might have and output a short search phrase that would return an exact competitor.

- role: user
  content: "{{input}}"

- role: system
  content: >
    Humanize the phrase by making it more natural and marketable. Add the prefix 'KEY:' at the beginning.
    Do not include the brand name. Don't add anything else.

- role: user
  content: "{{input}}"
      `,
      inputData: 'Nike Athletic Shoes'
    },
    {
      name: 'Product Analysis Chain',
      promptYaml: `
- role: system
  content: "Analyze the following product and provide insights about its market position."

- role: user
  content: "{{productName}}"
      `,
      inputData: { productName: 'Wireless Bluetooth Headphones' }
    },
    {
      name: 'Content Review Chain',
      promptYaml: `
- role: system
  content: "You are a content reviewer. Analyze the following content for quality and relevance."

- role: user
  content: "{{content}}"
      `,
      inputData: { content: sampleData.lexReviewData.reviewContent }
    }
  ];

  const results = [];

  for (let i = 0; i < Math.min(testCases.length, 3); i++) { // Limit to 3 tests
    const testCase = testCases[i];
    try {
      console.log(`   Testing: ${testCase.name}`);

      const startTime = Date.now();
      const result = await runPromptChain(testCase.promptYaml, testCase.inputData);
      const endTime = Date.now();

      const testResult = {
        testCase: testCase.name,
        success: true,
        responseTime: endTime - startTime,
        tokenUsage: {
          promptTokens: result.tokenUsage?.prompt_tokens || result.tokenUsage?.input_tokens || 0,
          completionTokens: result.tokenUsage?.completion_tokens || result.tokenUsage?.output_tokens || 0,
          totalTokens: result.tokenUsage?.total_tokens || 0,
          reasoning_tokens: result.tokenUsage?.reasoning_tokens || 0
        },
        cost: result.cost,
        output: result.output?.substring(0, 100) + '...',
        promptUsed: `Prompt Chain: ${testCase.promptYaml.replace(/\n/g, ' ')}`,
        modelUsed: result.model || 'azure-gpt4o'
      };

      results.push(testResult);
      testResults.summary.totalTokensUsed += result.tokenUsage?.total_tokens || 0;
      testResults.summary.totalCost += result.cost || 0;
      testResults.summary.successfulTests++;

      console.log(`   ✅ Success - Cost: $${result.cost?.toFixed(4) || 0}`);

    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      results.push({
        testCase: testCase.name,
        success: false,
        error: error.message
      });
      testResults.summary.failedTests++;
    }
  }

  testResults.functionTests.runPromptChain = results;
  testResults.summary.totalTests += results.length;
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
  console.log('\n📊 Generating Test Report...');

  const report = {
    ...testResults,
    summary: {
      ...testResults.summary,
      successRate: ((testResults.summary.successfulTests / testResults.summary.totalTests) * 100).toFixed(2) + '%',
      averageTokensPerTest: Math.round(testResults.summary.totalTokensUsed / testResults.summary.totalTests),
      estimatedCost: (testResults.summary.totalTokensUsed * 0.00002).toFixed(4) // Rough estimate
    }
  };

  // Save detailed report
  const reportPath = path.join(__dirname, `../reports/litellm-test-report-${Date.now()}.json`);

  // Ensure reports directory exists
  const reportsDir = path.dirname(reportPath);
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  // Generate detailed CSV with prompts and token breakdown
  const csvData = [];
  csvData.push(['Function Name', 'Model Name', 'Prompt Used', 'Response', 'Input Tokens', 'Output Tokens', 'Reasoning Tokens']);

  Object.entries(testResults.functionTests).forEach(([functionName, tests]) => {
    tests.forEach(test => {
      if (test.success) {
        const promptUsed = test.promptUsed || test.systemPrompt || test.userPrompt || 'N/A';
        const response = test.response || test.message || test.keyword || test.output || 'N/A';
        const inputTokens = test.tokenUsage?.promptTokens || test.tokenUsage?.input_tokens || 'N/A';
        const outputTokens = test.tokenUsage?.completionTokens || test.tokenUsage?.output_tokens || 'N/A';
        const reasoningTokens = test.tokenUsage?.reasoning_tokens || test.tokenUsage?.cached_tokens || 0;
        const modelName = test.model || test.modelUsed || 'gpt-4o'; // Default model if not specified

        csvData.push([
          functionName,
          modelName,
          `"${promptUsed.replace(/"/g, '""').replace(/\n/g, ' ').replace(/\r/g, '')}"`, // Escape quotes, keep full prompt
          `"${response.replace(/"/g, '""').replace(/\n/g, ' ').replace(/\r/g, '')}"`, // Escape quotes in response
          inputTokens,
          outputTokens,
          reasoningTokens
        ]);
      }
    });
  });

  const csvContent = csvData.map(row => row.join(',')).join('\n');
  const csvPath = path.join(__dirname, `../reports/litellm-token-analysis-${Date.now()}.csv`);
  fs.writeFileSync(csvPath, csvContent);

  // Generate summary CSV (original format)
  const summaryData = [];
  summaryData.push(['Function', 'Model', 'Test Case', 'Success', 'Response Time (ms)', 'Prompt Tokens', 'Completion Tokens', 'Total Tokens', 'Cost']);

  Object.entries(testResults.functionTests).forEach(([functionName, tests]) => {
    tests.forEach(test => {
      const modelName = test.model || test.modelUsed || 'gpt-4o';
      summaryData.push([
        functionName,
        modelName,
        test.testCase || test.name,
        test.success ? 'Yes' : 'No',
        test.responseTime || 'N/A',
        test.tokenUsage?.promptTokens || 'N/A',
        test.tokenUsage?.completionTokens || 'N/A',
        test.tokenUsage?.totalTokens || 'N/A',
        test.cost || 'N/A'
      ]);
    });
  });

  const summaryContent = summaryData.map(row => row.join(',')).join('\n');
  const summaryPath = path.join(__dirname, `../reports/litellm-test-summary-${Date.now()}.csv`);
  fs.writeFileSync(summaryPath, summaryContent);

  console.log(`📄 Detailed report saved: ${reportPath}`);
  console.log(`📊 Token analysis CSV saved: ${csvPath}`);
  console.log(`📋 Test summary CSV saved: ${summaryPath}`);

  return report;
}

/**
 * Print test summary to console
 */
function printTestSummary(report) {
  console.log('\n' + '='.repeat(60));
  console.log('🎯 LITELLM TESTING SUMMARY');
  console.log('='.repeat(60));
  console.log(`📅 Test Date: ${new Date(report.timestamp).toLocaleString()}`);
  console.log(`🔧 Available Models: ${report.availableModels.length}`);
  console.log(`🧪 Total Tests: ${report.summary.totalTests}`);
  console.log(`✅ Successful: ${report.summary.successfulTests}`);
  console.log(`❌ Failed: ${report.summary.failedTests}`);
  console.log(`📈 Success Rate: ${report.summary.successRate}`);
  console.log(`🎫 Total Tokens Used: ${report.summary.totalTokensUsed.toLocaleString()}`);
  console.log(`💰 Estimated Cost: $${report.summary.estimatedCost}`);
  console.log(`📊 Avg Tokens/Test: ${report.summary.averageTokensPerTest}`);

  console.log('\n📋 Function Test Results:');
  Object.entries(report.functionTests).forEach(([functionName, tests]) => {
    const successful = tests.filter(t => t.success).length;
    const total = tests.length;
    const totalTokens = tests.reduce((sum, t) => sum + (t.tokenUsage?.totalTokens || 0), 0);

    console.log(`   ${functionName}: ${successful}/${total} (${totalTokens} tokens)`);
  });

  console.log('\n🎯 Top Token Consumers:');
  const allTests = Object.values(report.functionTests).flat();
  const sortedByTokens = allTests
    .filter(t => t.success && t.tokenUsage?.totalTokens)
    .sort((a, b) => (b.tokenUsage?.totalTokens || 0) - (a.tokenUsage?.totalTokens || 0))
    .slice(0, 5);

  sortedByTokens.forEach((test, index) => {
    console.log(`   ${index + 1}. ${test.testCase || test.name}: ${test.tokenUsage.totalTokens} tokens`);
  });

  console.log('\n' + '='.repeat(60));
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting LiteLLM Testing Script for Jeff AI Functions');
  console.log('=' .repeat(60));

  try {
    // Step 1: Fetch available models
    await fetchAvailableModels();

    // Step 2: Run all function tests
    await testGetChatGPTResponse();
    await testCompletionFactory();
    await testMultiProviderAI();
    await testLiteLLMGatewayModels();
    await testHumanizationFunctions();
    await testGetKeyword();
    await testRunPromptChain();

    // Step 3: Generate and display results
    const report = generateTestReport();
    printTestSummary(report);

    console.log('\n🎉 Testing completed successfully!');

  } catch (error) {
    console.error('\n💥 Testing failed with error:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = {
  main,
  fetchAvailableModels,
  testGetChatGPTResponse,
  testCompletionFactory,
  testMultiProviderAI,
  testLiteLLMGatewayModels,
  testHumanizationFunctions,
  testGetKeyword,
  testRunPromptChain,
  generateTestReport,
  printTestSummary
};

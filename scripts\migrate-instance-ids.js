const prisma = require('../src/database/prisma/getPrismaClient');

// Mapping of AWS instance IDs to Azure VM names
const AWS_TO_AZURE_MAPPING = {
  'i-0eca89a83b7c830be': 'jeff-worker-1',
  'i-0b9f2d0b5678231ec': 'jeff-worker-2',
  'i-0d480a46ac9f608ac': 'jeff-worker-3',
  'i-0ce83b7b014f47885': 'jeff-worker-4'
};

async function migrateInstanceIds() {
  console.log('🚀 Starting AWS to Azure instance ID migration...');
  
  try {
    // Update configurations table
    console.log('📊 Updating Configurations table...');
    const configs = await prisma.configurations.findMany();
    let configUpdatedCount = 0;
    
    for (const config of configs) {
      let updated = false;
      let scraperApi = { ...config.scraperApi };
      
      // Update instance IDs in scraperApi config
      if (scraperApi.instanceIds && Array.isArray(scraperApi.instanceIds)) {
        const originalIds = [...scraperApi.instanceIds];
        scraperApi.instanceIds = scraperApi.instanceIds.map(id => 
          AWS_TO_AZURE_MAPPING[id] || id
        );
        
        // Check if any changes were made
        if (JSON.stringify(originalIds) !== JSON.stringify(scraperApi.instanceIds)) {
          updated = true;
          console.log(`  ✅ Configuration ${config.id}: ${originalIds.join(', ')} → ${scraperApi.instanceIds.join(', ')}`);
        }
      }
      
      // Update any AWS region references
      if (scraperApi.awsRegion) {
        scraperApi.azureLocation = 'Central India';
        delete scraperApi.awsRegion;
        updated = true;
        console.log(`  ✅ Configuration ${config.id}: Updated region to Azure location`);
      }
      
      if (updated) {
        await prisma.configurations.update({
          where: { id: config.id },
          data: { scraperApi }
        });
        configUpdatedCount++;
      }
    }
    
    console.log(`📊 Updated ${configUpdatedCount} configurations`);
    
    // Update JobCentral table if it has instance references
    console.log('📊 Checking JobCentral table for instance references...');
    const jobs = await prisma.jobCentral.findMany({
      where: {
        config: {
          path: ['instanceIds'],
          not: null
        }
      }
    });
    
    let jobUpdatedCount = 0;
    for (const job of jobs) {
      let config = { ...job.config };
      let updated = false;
      
      if (config.instanceIds && Array.isArray(config.instanceIds)) {
        const originalIds = [...config.instanceIds];
        config.instanceIds = config.instanceIds.map(id => 
          AWS_TO_AZURE_MAPPING[id] || id
        );
        
        if (JSON.stringify(originalIds) !== JSON.stringify(config.instanceIds)) {
          updated = true;
          console.log(`  ✅ Job ${job.id}: ${originalIds.join(', ')} → ${config.instanceIds.join(', ')}`);
        }
      }
      
      if (updated) {
        await prisma.jobCentral.update({
          where: { id: job.id },
          data: { config }
        });
        jobUpdatedCount++;
      }
    }
    
    console.log(`📊 Updated ${jobUpdatedCount} job configurations`);
    
    // Search for any other potential AWS instance ID references
    console.log('🔍 Searching for other potential AWS references...');
    
    // Check for hardcoded instance IDs in text fields
    const awsInstancePattern = /i-[0-9a-f]{17}/g;
    
    // Check User table for any AWS references in JSON fields
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { preferences: { not: null } },
          { settings: { not: null } }
        ]
      }
    });
    
    let userUpdatedCount = 0;
    for (const user of users) {
      let updated = false;
      let preferences = user.preferences ? { ...user.preferences } : null;
      let settings = user.settings ? { ...user.settings } : null;
      
      // Update preferences if they contain AWS instance IDs
      if (preferences && JSON.stringify(preferences).match(awsInstancePattern)) {
        let preferencesStr = JSON.stringify(preferences);
        Object.entries(AWS_TO_AZURE_MAPPING).forEach(([awsId, azureId]) => {
          preferencesStr = preferencesStr.replace(new RegExp(awsId, 'g'), azureId);
        });
        preferences = JSON.parse(preferencesStr);
        updated = true;
        console.log(`  ✅ User ${user.id}: Updated preferences with Azure VM names`);
      }
      
      // Update settings if they contain AWS instance IDs
      if (settings && JSON.stringify(settings).match(awsInstancePattern)) {
        let settingsStr = JSON.stringify(settings);
        Object.entries(AWS_TO_AZURE_MAPPING).forEach(([awsId, azureId]) => {
          settingsStr = settingsStr.replace(new RegExp(awsId, 'g'), azureId);
        });
        settings = JSON.parse(settingsStr);
        updated = true;
        console.log(`  ✅ User ${user.id}: Updated settings with Azure VM names`);
      }
      
      if (updated) {
        await prisma.user.update({
          where: { id: user.id },
          data: { preferences, settings }
        });
        userUpdatedCount++;
      }
    }
    
    console.log(`📊 Updated ${userUpdatedCount} user records`);
    
    // Summary
    console.log('\n📋 Migration Summary:');
    console.log(`  • Configurations updated: ${configUpdatedCount}`);
    console.log(`  • Jobs updated: ${jobUpdatedCount}`);
    console.log(`  • Users updated: ${userUpdatedCount}`);
    console.log('\n✅ AWS to Azure instance ID migration completed successfully!');
    
    // Show mapping used
    console.log('\n🗺️  Instance ID Mapping Used:');
    Object.entries(AWS_TO_AZURE_MAPPING).forEach(([awsId, azureId]) => {
      console.log(`  ${awsId} → ${azureId}`);
    });
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateInstanceIds()
    .catch(console.error)
    .finally(() => prisma.$disconnect());
}

module.exports = { migrateInstanceIds, AWS_TO_AZURE_MAPPING };

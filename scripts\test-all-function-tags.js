/**
 * Test All Function Tags - Verify they match the sheet exactly
 * This script tests all functions to ensure tags match the sheet requirements
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

// Expected tags from the sheet (last column)
const expectedTags = {
  'getBrandedKeyword': 'PPCAudit',
  'getMainImageOptimisation': 'MainImageOptimisation', 
  'getProductTitleReport_amazonAudit': 'BSRCategoryMatch',
  'getProductTitleReport_dynamicAudit': 'BSRCategoryMatchDynamicAudit',
  'processAuditData_productTitleHumanisation': 'ProspectProductTitleHumanisation',
  'processAuditData_bsrCategoryUsingGpt': 'BSRCategoryUsingGPT',
  'processAuditData_caseStudies': 'CaseStudies',
  'fetchCompAmazonData': 'CompSearchKeyword',
  'processCompetitionData_companyNameHumanisation': 'CompetitorCompanyNameHumanisation',
  'processCompetitionData_productTitleHumanisation': 'CompetitorProductTitleHumanisation'
};

async function testAllFunctionTags() {
  console.log('🧪 Testing All Function Tags Against Sheet Requirements');
  console.log('=' .repeat(80));
  
  const results = [];
  let allPassed = true;

  // Test 1: getBrandedKeyword
  console.log('\n📋 Test 1: getBrandedKeyword function');
  console.log('-'.repeat(50));
  
  try {
    const result1 = await getChatGPTResponse(
      'Test prompt',
      'Test data',
      1,
      {
        functionName: "getBrandedKeyword",
        customTags: [
          `ClientId:1`,
          `PPCAudit`,
          `AmazonAudit`,
          `GetBrandedKeyword`,
        ],
      }
    );
    
    console.log('📤 Actual tags:', JSON.stringify(result1.metadata.tags, null, 2));
    
    const expectedStructure = [
      'Jeff',
      'ScrapeGPT', 
      'GetChatGPTResponse',
      'ClientId:1',
      'PPCAudit',
      'AmazonAudit', 
      'GetBrandedKeyword'
    ];
    
    const passed = JSON.stringify(result1.metadata.tags) === JSON.stringify(expectedStructure);
    console.log(`✅ Expected: ${JSON.stringify(expectedStructure)}`);
    console.log(`${passed ? '✅' : '❌'} Match: ${passed}`);
    console.log(`🏷️  Last column tag (PPCAudit): ${result1.metadata.tags.includes('PPCAudit') ? '✅' : '❌'}`);
    
    // Check for duplicates
    const hasDuplicates = result1.metadata.tags.length !== new Set(result1.metadata.tags).size;
    console.log(`🔄 No duplicates: ${!hasDuplicates ? '✅' : '❌'}`);
    
    results.push({
      function: 'getBrandedKeyword',
      passed: passed && !hasDuplicates,
      tags: result1.metadata.tags,
      expectedLastColumnTag: 'PPCAudit',
      hasLastColumnTag: result1.metadata.tags.includes('PPCAudit'),
      hasDuplicates
    });
    
    if (!passed || hasDuplicates) allPassed = false;
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    allPassed = false;
  }

  // Test 2: Simulate other functions by checking their tag structure
  console.log('\n📋 Test 2: Simulating other function tag structures');
  console.log('-'.repeat(50));
  
  const functionTests = [
    {
      name: 'getMainImageOptimisation',
      customTags: [`ClientId:1`, `MainImageOptimisation`, `AmazonAudit`, `GetMainImageOptimisation`],
      expectedLastTag: 'MainImageOptimisation'
    },
    {
      name: 'getProductTitleReport_amazonAudit', 
      customTags: [`ClientId:1`, `BSRCategoryMatch`, `AmazonAudit`, `GetProductTitleReport`],
      expectedLastTag: 'BSRCategoryMatch'
    },
    {
      name: 'getProductTitleReport_dynamicAudit',
      customTags: [`ClientId:1`, `BSRCategoryMatchDynamicAudit`, `AmazonAudit`, `GetProductTitleReport`],
      expectedLastTag: 'BSRCategoryMatchDynamicAudit'
    },
    {
      name: 'processAuditData_productTitleHumanisation',
      customTags: [`ClientId:1`, `ProspectProductTitleHumanisation`, `AmazonAudit`, `ProcessAuditData`],
      expectedLastTag: 'ProspectProductTitleHumanisation'
    },
    {
      name: 'processAuditData_bsrCategoryUsingGpt',
      customTags: [`ClientId:1`, `BSRCategoryUsingGPT`, `AmazonAudit`, `ProcessAuditData`],
      expectedLastTag: 'BSRCategoryUsingGPT'
    },
    {
      name: 'processAuditData_caseStudies',
      customTags: [`ClientId:1`, `CaseStudies`, `AmazonAudit`, `ProcessAuditData`],
      expectedLastTag: 'CaseStudies'
    },
    {
      name: 'fetchCompAmazonData',
      customTags: [`ClientId:1`, `CompSearchKeyword`, `CompetitionData`, `FetchCompAmazonData`],
      expectedLastTag: 'CompSearchKeyword'
    },
    {
      name: 'processCompetitionData_companyNameHumanisation',
      customTags: [`ClientId:1`, `CompetitorCompanyNameHumanisation`, `CompetitionData`, `ProcessCompetitionData`],
      expectedLastTag: 'CompetitorCompanyNameHumanisation'
    },
    {
      name: 'processCompetitionData_productTitleHumanisation',
      customTags: [`ClientId:1`, `CompetitorProductTitleHumanisation`, `CompetitionData`, `ProcessCompetitionData`],
      expectedLastTag: 'CompetitorProductTitleHumanisation'
    }
  ];

  for (const test of functionTests) {
    try {
      const result = await getChatGPTResponse(
        'Test prompt',
        'Test data', 
        1,
        {
          functionName: test.name,
          customTags: test.customTags
        }
      );
      
      console.log(`\n🔍 ${test.name}:`);
      console.log(`   Tags: ${JSON.stringify(result.metadata.tags)}`);
      
      const expectedStructure = ['Jeff', 'ScrapeGPT', 'GetChatGPTResponse', ...test.customTags];
      const passed = JSON.stringify(result.metadata.tags) === JSON.stringify(expectedStructure);
      const hasLastColumnTag = result.metadata.tags.includes(test.expectedLastTag);
      const hasDuplicates = result.metadata.tags.length !== new Set(result.metadata.tags).size;
      
      console.log(`   Expected last column tag (${test.expectedLastTag}): ${hasLastColumnTag ? '✅' : '❌'}`);
      console.log(`   No duplicates: ${!hasDuplicates ? '✅' : '❌'}`);
      console.log(`   Structure match: ${passed ? '✅' : '❌'}`);
      
      results.push({
        function: test.name,
        passed: passed && !hasDuplicates && hasLastColumnTag,
        tags: result.metadata.tags,
        expectedLastColumnTag: test.expectedLastTag,
        hasLastColumnTag,
        hasDuplicates
      });
      
      if (!passed || hasDuplicates || !hasLastColumnTag) allPassed = false;
      
    } catch (error) {
      console.log(`❌ ${test.name} Error:`, error.message);
      allPassed = false;
    }
  }

  // Summary
  console.log('\n📊 SUMMARY REPORT');
  console.log('=' .repeat(80));
  
  console.log('\n🏷️  TAG VALIDATION RESULTS:');
  results.forEach(result => {
    console.log(`   ${result.passed ? '✅' : '❌'} ${result.function}`);
    if (!result.hasLastColumnTag) {
      console.log(`      ⚠️  Missing expected tag: ${result.expectedLastColumnTag}`);
    }
    if (result.hasDuplicates) {
      console.log(`      ⚠️  Has duplicate tags`);
    }
  });
  
  console.log(`\n🎯 OVERALL RESULT: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (!allPassed) {
    console.log('\n🔧 ISSUES FOUND:');
    results.filter(r => !r.passed).forEach(result => {
      console.log(`   • ${result.function}: ${!result.hasLastColumnTag ? 'Missing last column tag' : ''} ${result.hasDuplicates ? 'Has duplicates' : ''}`);
    });
  }
  
  return { allPassed, results };
}

if (require.main === module) {
  testAllFunctionTags().catch(console.error);
}

module.exports = { testAllFunctionTags };

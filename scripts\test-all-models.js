/**
 * Test that all models work with getChatGPTResponse
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');
const axios = require('axios');

const LITELLM_BASE_URL = 'https://ai.gateway.equalcollective.com';
const LITELLM_API_KEY = process.env.LITELLM_API_KEY;

async function fetchAvailableModels() {
  try {
    const response = await axios.get(`${LITELLM_BASE_URL}/v1/models`, {
      headers: {
        'Authorization': `Bearer ${LITELLM_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching models:', error.message);
    return [];
  }
}

async function testAllModels() {
  console.log('🧪 Testing ALL Models with getChatGPTResponse');
  console.log('=' .repeat(50));
  
  const models = await fetchAvailableModels();
  console.log(`Found ${models.length} models: ${models.map(m => m.id).join(', ')}`);
  
  const testPrompt = {
    system: 'You are a helpful assistant. Respond with exactly "Hello from [MODEL_NAME]" where [MODEL_NAME] is the model you are.',
    user: 'Say hello and identify yourself.'
  };
  
  const results = [];
  
  for (const model of models) {
    try {
      console.log(`\n🔍 Testing model: ${model.id}`);
      
      const startTime = Date.now();
      const result = await getChatGPTResponse(
        testPrompt.system,
        testPrompt.user,
        1, // clientId
        {
          model: model.id,
          temperature: 0.1,
          max_tokens: 50,
          customTags: [`test:allModels`, `model:${model.id}`]
        }
      );
      const endTime = Date.now();
      
      console.log(`   ✅ Success: "${result.message}"`);
      console.log(`   🎫 Tokens: ${result.total_tokens} (${endTime - startTime}ms)`);
      
      results.push({
        model: model.id,
        success: true,
        response: result.message,
        tokens: result.total_tokens,
        responseTime: endTime - startTime,
        tokenBreakdown: {
          prompt: result.prompt_tokens,
          completion: result.completion_tokens,
          reasoning: result.reasoning_tokens || 0
        }
      });
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      results.push({
        model: model.id,
        success: false,
        error: error.message
      });
    }
  }
  
  // Generate summary
  console.log('\n📊 Test Summary:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎯 Working Models:');
    successful.forEach(result => {
      console.log(`   ${result.model}: ${result.tokens} tokens (${result.responseTime}ms)`);
    });
    
    const totalTokens = successful.reduce((sum, r) => sum + r.tokens, 0);
    const avgTokens = Math.round(totalTokens / successful.length);
    const avgTime = Math.round(successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length);
    
    console.log(`\n📈 Averages: ${avgTokens} tokens, ${avgTime}ms response time`);
  }
  
  if (failed.length > 0) {
    console.log('\n💥 Failed Models:');
    failed.forEach(result => {
      console.log(`   ${result.model}: ${result.error}`);
    });
  }
  
  // Generate CSV for working models
  if (successful.length > 0) {
    console.log('\n📋 CSV Format:');
    console.log('Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens');
    
    successful.forEach(result => {
      const promptUsed = `"${testPrompt.system} | ${testPrompt.user}"`;
      console.log(`getChatGPTResponse,${result.model},${promptUsed},${result.tokenBreakdown.prompt},${result.tokenBreakdown.completion},${result.tokenBreakdown.reasoning}`);
    });
  }
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    workingModels: successful.map(r => r.model),
    failedModels: failed.map(r => r.model)
  };
}

async function main() {
  const results = await testAllModels();
  
  console.log('\n🎉 Test Complete!');
  console.log(`Working models: ${results.workingModels.join(', ')}`);
  
  if (results.failedModels.length > 0) {
    console.log(`Failed models: ${results.failedModels.join(', ')}`);
  }
  
  if (results.successful > 0) {
    console.log('\n✅ Ready to run full testing script with all working models!');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  testAllModels,
  fetchAvailableModels
};

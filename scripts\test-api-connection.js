/**
 * Test API connection to LiteLLM gateway
 */

require('dotenv').config();
const axios = require('axios');

const LITELLM_BASE_URL = 'https://ai.gateway.equalcollective.com';
const LITELLM_API_KEY = process.env.LITELLM_API_KEY;

async function testAPIConnection() {
  console.log('🔍 Testing LiteLLM API Connection...');
  console.log(`API Key: ${LITELLM_API_KEY ? LITELLM_API_KEY.substring(0, 10) + '...' : 'MISSING'}`);
  console.log(`Base URL: ${LITELLM_BASE_URL}`);
  
  try {
    // Test 1: Fetch available models
    console.log('\n📋 Test 1: Fetching available models...');
    const modelsResponse = await axios.get(`${LITELLM_BASE_URL}/v1/models`, {
      headers: {
        'Authorization': `Bearer ${LITELLM_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log(`✅ Models endpoint successful: ${modelsResponse.status}`);
    console.log(`📊 Found ${modelsResponse.data.data?.length || 0} models`);
    
    if (modelsResponse.data.data?.length > 0) {
      console.log('🎯 Available models:');
      modelsResponse.data.data.slice(0, 5).forEach(model => {
        console.log(`   - ${model.id}`);
      });
    }
    
    // Test 2: Simple chat completion
    console.log('\n💬 Test 2: Simple chat completion...');
    const chatResponse = await axios.post(`${LITELLM_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: 'Say "Hello from LiteLLM test!" in exactly those words.'
        }
      ],
      max_tokens: 50,
      temperature: 0.1,
      metadata: {
        tags: ['jeff', 'test', 'apiConnection']
      }
    }, {
      headers: {
        'Authorization': `Bearer ${LITELLM_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log(`✅ Chat completion successful: ${chatResponse.status}`);
    console.log(`💭 Response: ${chatResponse.data.choices[0]?.message?.content}`);
    console.log(`🎫 Token usage:`, chatResponse.data.usage);
    
    console.log('\n🎉 All API tests passed! LiteLLM connection is working.');
    return true;
    
  } catch (error) {
    console.error('\n❌ API test failed:');
    console.error(`Status: ${error.response?.status}`);
    console.error(`Message: ${error.message}`);
    console.error(`Response data:`, error.response?.data);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused - check if the LiteLLM gateway is accessible');
    } else if (error.response?.status === 401) {
      console.error('🔑 Authentication failed - check your API key');
    } else if (error.response?.status === 403) {
      console.error('🚫 Forbidden - check your API key permissions');
    } else if (error.code === 'ENOTFOUND') {
      console.error('🌐 DNS resolution failed - check the URL');
    }
    
    return false;
  }
}

// Test the getChatGPTResponse function directly
async function testGetChatGPTResponseDirect() {
  console.log('\n🧪 Testing getChatGPTResponse function directly...');
  
  try {
    const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');
    
    const result = await getChatGPTResponse(
      'You are a helpful assistant.',
      'Say "Direct function test successful!" in exactly those words.',
      1, // clientId
      {
        model: 'gpt-4o',
        temperature: 0.1,
        max_tokens: 50,
        customTags: ['test:direct']
      }
    );
    
    console.log('✅ Direct getChatGPTResponse test successful!');
    console.log(`💭 Response: ${result.message}`);
    console.log(`🎫 Tokens: ${result.total_tokens}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Direct getChatGPTResponse test failed:');
    console.error(`Error: ${error.message}`);
    console.error(`Stack: ${error.stack}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting LiteLLM API Connection Tests');
  console.log('=' .repeat(50));
  
  const apiTest = await testAPIConnection();
  const directTest = await testGetChatGPTResponseDirect();
  
  console.log('\n📊 Test Summary:');
  console.log(`   API Connection: ${apiTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Direct Function: ${directTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (apiTest && directTest) {
    console.log('\n🎉 All tests passed! Ready to run the full testing script.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above before running the full script.');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  testAPIConnection,
  testGetChatGPTResponseDirect
};

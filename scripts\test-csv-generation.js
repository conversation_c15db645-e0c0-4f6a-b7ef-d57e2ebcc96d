/**
 * Test CSV generation functionality
 */

const fs = require('fs');
const path = require('path');

// Mock test results to verify CSV generation
const mockTestResults = {
  timestamp: new Date().toISOString(),
  availableModels: [
    { id: 'gpt-4o', object: 'model', created: 1234567890, owned_by: 'openai' },
    { id: 'claude-3-5-sonnet', object: 'model', created: 1234567890, owned_by: 'anthropic' }
  ],
  functionTests: {
    getChatGPTResponse: [
      {
        testCase: 'Amazon Product Analysis',
        success: true,
        responseTime: 1250,
        tokenUsage: {
          promptTokens: 150,
          completionTokens: 95,
          totalTokens: 245,
          reasoning_tokens: 0
        },
        response: 'This Nike Air Max 270 running shoe is designed for comfort and performance...',
        promptUsed: 'System: You will receive Amazon product data... | User: {"title":"Nike Men\'s Air Max 270..."}',
        modelUsed: 'gpt-4o'
      },
      {
        testCase: 'Company Name Analysis',
        success: true,
        responseTime: 890,
        tokenUsage: {
          promptTokens: 85,
          completionTokens: 71,
          totalTokens: 156,
          reasoning_tokens: 0
        },
        response: 'Nike Inc. is a multinational corporation...',
        promptUsed: 'System: Analyze the following company name... | User: Nike Inc.',
        modelUsed: 'gpt-4o'
      }
    ],
    completionFactory: [
      {
        testCase: 'Competition Search Keyword',
        type: 'compSearchKeyword',
        success: true,
        responseTime: 1100,
        tokenUsage: {
          promptTokens: 120,
          completionTokens: 69,
          totalTokens: 189,
          reasoning_tokens: 0
        },
        response: 'athletic running shoes',
        promptUsed: 'Factory Type: compSearchKeyword | Data: {"title":"Nike Men\'s Air Max 270..."}',
        modelUsed: 'gpt-4o'
      }
    ],
    multiProviderAI: [
      {
        testCase: 'Content Analysis',
        model: 'azure-gpt4o',
        success: true,
        responseTime: 1350,
        tokenUsage: {
          promptTokens: 95,
          completionTokens: 78,
          totalTokens: 173,
          reasoning_tokens: 12
        },
        response: 'The review content shows positive sentiment...',
        promptUsed: 'System: You are an expert content analyst. | User: Analyze this review: "These headphones..."',
        modelUsed: 'azure-gpt4o'
      }
    ],
    humanizationFunctions: [
      {
        function: 'humanizeCompanyName',
        input: 'NIKE, INC.',
        success: true,
        responseTime: 750,
        tokenUsage: {
          promptTokens: 45,
          completionTokens: 25,
          totalTokens: 70,
          reasoning_tokens: 0
        },
        response: 'Nike',
        promptUsed: 'Humanize company name: NIKE, INC.',
        modelUsed: 'gpt-4o'
      }
    ],
    getKeyword: [
      {
        testCase: 'Amazon Product Data',
        success: true,
        responseTime: 980,
        tokenUsage: {
          promptTokens: 110,
          completionTokens: 45,
          totalTokens: 155,
          reasoning_tokens: 0
        },
        keyword: 'running shoes',
        promptUsed: 'Extract keyword from: {"title":"Nike Men\'s Air Max 270 Running Shoes..."}',
        modelUsed: 'gpt-4o'
      }
    ]
  },
  summary: {
    totalTests: 6,
    successfulTests: 6,
    failedTests: 0,
    totalTokensUsed: 988,
    totalCost: 0.0198
  }
};

/**
 * Generate the CSV with requested columns
 */
function generateTokenAnalysisCSV(testResults) {
  console.log('🔍 Generating Token Analysis CSV...');

  const csvData = [];
  csvData.push(['Function Name', 'Model Name', 'Prompt Used', 'Input Tokens', 'Output Tokens', 'Reasoning Tokens']);

  Object.entries(testResults.functionTests).forEach(([functionName, tests]) => {
    tests.forEach(test => {
      if (test.success) {
        const promptUsed = test.promptUsed || test.systemPrompt || test.userPrompt || 'N/A';
        const inputTokens = test.tokenUsage?.promptTokens || test.tokenUsage?.input_tokens || 'N/A';
        const outputTokens = test.tokenUsage?.completionTokens || test.tokenUsage?.output_tokens || 'N/A';
        const reasoningTokens = test.tokenUsage?.reasoning_tokens || test.tokenUsage?.cached_tokens || 0;
        const modelName = test.model || test.modelUsed || 'gpt-4o';

        // Clean and escape the prompt for CSV
        const cleanPrompt = promptUsed.substring(0, 200).replace(/"/g, '""').replace(/\n/g, ' ').replace(/\r/g, '');

        csvData.push([
          functionName,
          modelName,
          `"${cleanPrompt}..."`,
          inputTokens,
          outputTokens,
          reasoningTokens
        ]);
      }
    });
  });
  
  const csvContent = csvData.map(row => row.join(',')).join('\n');
  
  // Ensure reports directory exists
  const reportsDir = path.join(__dirname, '../reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  const csvPath = path.join(reportsDir, `token-analysis-test-${Date.now()}.csv`);
  fs.writeFileSync(csvPath, csvContent);
  
  console.log(`✅ Token Analysis CSV saved: ${csvPath}`);
  console.log('\n📊 CSV Preview:');
  console.log(csvData.slice(0, 4).map(row => row.join(' | ')).join('\n'));
  
  return csvPath;
}

/**
 * Test the CSV generation
 */
function testCSVGeneration() {
  console.log('🧪 Testing CSV Generation for Token Analysis');
  console.log('=' .repeat(50));
  
  try {
    const csvPath = generateTokenAnalysisCSV(mockTestResults);
    
    // Read and display the generated CSV
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.split('\n');
    
    console.log('\n📋 Generated CSV Content:');
    console.log('=' .repeat(50));
    lines.slice(0, 10).forEach((line, index) => {
      console.log(`${index + 1}: ${line}`);
    });
    
    if (lines.length > 10) {
      console.log(`... and ${lines.length - 10} more lines`);
    }
    
    console.log('\n✅ CSV generation test completed successfully!');
    console.log(`📁 File saved at: ${csvPath}`);
    
    // Analyze the results
    const dataLines = lines.slice(1).filter(line => line.trim()); // Skip header and empty lines
    console.log(`\n📊 Analysis:`);
    console.log(`   - Header row: ✅`);
    console.log(`   - Data rows: ${dataLines.length}`);
    console.log(`   - Functions tested: ${new Set(dataLines.map(line => line.split(',')[0])).size}`);
    
    // Show token distribution
    const tokenData = dataLines.map(line => {
      const parts = line.split(',');
      return {
        function: parts[0],
        inputTokens: parseInt(parts[2]) || 0,
        outputTokens: parseInt(parts[3]) || 0,
        reasoningTokens: parseInt(parts[4]) || 0
      };
    });
    
    const totalInputTokens = tokenData.reduce((sum, item) => sum + item.inputTokens, 0);
    const totalOutputTokens = tokenData.reduce((sum, item) => sum + item.outputTokens, 0);
    const totalReasoningTokens = tokenData.reduce((sum, item) => sum + item.reasoningTokens, 0);
    
    console.log(`   - Total Input Tokens: ${totalInputTokens}`);
    console.log(`   - Total Output Tokens: ${totalOutputTokens}`);
    console.log(`   - Total Reasoning Tokens: ${totalReasoningTokens}`);
    console.log(`   - Grand Total: ${totalInputTokens + totalOutputTokens + totalReasoningTokens}`);
    
  } catch (error) {
    console.error('❌ CSV generation test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
if (require.main === module) {
  testCSVGeneration();
}

module.exports = {
  generateTokenAnalysisCSV,
  testCSVGeneration
};

/**
 * Test Dashboard Tags - Run all functions with sample data to see actual dashboard tags
 * This script will make real API calls to show you exactly what tags appear on LiteLLM dashboard
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

async function testDashboardTags() {
  console.log('🚀 TESTING ALL FUNCTIONS WITH SAMPLE DATA');
  console.log('📊 These are the EXACT tags that will appear on your LiteLLM dashboard');
  console.log('=' .repeat(80));
  
  const results = [];
  
  // Test 1: getBrandedKeyword
  console.log('\n1️⃣  TESTING: getBrandedKeyword');
  console.log('📝 Sample Data: Nike Running Shoes');
  console.log('-'.repeat(50));
  
  try {
    const result1 = await getChatGPTResponse(
      'Generate branded and non-branded keywords for PPC audit',
      JSON.stringify({
        "Brand Name": "Nike",
        "Product Title": "Nike Men's Air Max 270 Running Shoes"
      }),
      1,
      {
        functionName: "getBrandedKeyword",
        customTags: [
          `ClientId:1`,
          `PPCAudit`,
          `AmazonAudit`,
          `GetBrandedKeyword`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result1.metadata.tags, null, 2));
    console.log('💰 Cost:', result1.total_tokens, 'tokens');
    console.log('⚡ Response:', result1.message.substring(0, 100) + '...');
    
    results.push({
      function: 'getBrandedKeyword',
      tags: result1.metadata.tags,
      tokens: result1.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'getBrandedKeyword', success: false, error: error.message });
  }

  // Test 2: getMainImageOptimisation
  console.log('\n2️⃣  TESTING: getMainImageOptimisation');
  console.log('📝 Sample Data: Product Image Analysis');
  console.log('-'.repeat(50));
  
  try {
    const result2 = await getChatGPTResponse(
      'Analyze and optimize main product image',
      JSON.stringify({
        "Image": "https://example.com/product-image.jpg",
        "Title": "Wireless Bluetooth Headphones",
        "About this item": "Premium sound quality with noise cancellation"
      }),
      1,
      {
        functionName: "getMainImageOptimisation",
        customTags: [
          `ClientId:1`,
          `MainImageOptimisation`,
          `AmazonAudit`,
          `GetMainImageOptimisation`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result2.metadata.tags, null, 2));
    console.log('💰 Cost:', result2.total_tokens, 'tokens');
    console.log('⚡ Response:', result2.message.substring(0, 100) + '...');
    
    results.push({
      function: 'getMainImageOptimisation',
      tags: result2.metadata.tags,
      tokens: result2.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'getMainImageOptimisation', success: false, error: error.message });
  }

  // Test 3: getProductTitleReport (Amazon Audit)
  console.log('\n3️⃣  TESTING: getProductTitleReport (Amazon Audit)');
  console.log('📝 Sample Data: BSR Category Matching');
  console.log('-'.repeat(50));
  
  try {
    const result3 = await getChatGPTResponse(
      'Match product category with BSR data',
      'Electronics > Headphones > Wireless',
      1,
      {
        functionName: "getProductTitleReport",
        customTags: [
          `ClientId:1`,
          `BSRCategoryMatch`,
          `AmazonAudit`,
          `GetProductTitleReport`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result3.metadata.tags, null, 2));
    console.log('💰 Cost:', result3.total_tokens, 'tokens');
    console.log('⚡ Response:', result3.message.substring(0, 100) + '...');
    
    results.push({
      function: 'getProductTitleReport_amazonAudit',
      tags: result3.metadata.tags,
      tokens: result3.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'getProductTitleReport_amazonAudit', success: false, error: error.message });
  }

  // Test 4: getProductTitleReport (Dynamic Audit)
  console.log('\n4️⃣  TESTING: getProductTitleReport (Dynamic Audit)');
  console.log('📝 Sample Data: Dynamic BSR Category Matching');
  console.log('-'.repeat(50));
  
  try {
    const result4 = await getChatGPTResponse(
      'Match product category with BSR data for dynamic audit',
      'Sports & Outdoors > Exercise & Fitness > Cardio Training',
      1,
      {
        functionName: "getProductTitleReport",
        customTags: [
          `ClientId:1`,
          `BSRCategoryMatchDynamicAudit`,
          `AmazonAudit`,
          `GetProductTitleReport`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result4.metadata.tags, null, 2));
    console.log('💰 Cost:', result4.total_tokens, 'tokens');
    console.log('⚡ Response:', result4.message.substring(0, 100) + '...');
    
    results.push({
      function: 'getProductTitleReport_dynamicAudit',
      tags: result4.metadata.tags,
      tokens: result4.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'getProductTitleReport_dynamicAudit', success: false, error: error.message });
  }

  // Test 5: processAuditData (Product Title Humanisation)
  console.log('\n5️⃣  TESTING: processAuditData (Product Title Humanisation)');
  console.log('📝 Sample Data: Product Title Humanization');
  console.log('-'.repeat(50));
  
  try {
    const result5 = await getChatGPTResponse(
      'Humanize product title for audit reports',
      'ACME CORP WIRELESS BLUETOOTH HEADPHONES WITH NOISE CANCELLATION TECHNOLOGY',
      1,
      {
        functionName: "processAuditData",
        customTags: [
          `ClientId:1`,
          `ProspectProductTitleHumanisation`,
          `AmazonAudit`,
          `ProcessAuditData`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result5.metadata.tags, null, 2));
    console.log('💰 Cost:', result5.total_tokens, 'tokens');
    console.log('⚡ Response:', result5.message.substring(0, 100) + '...');
    
    results.push({
      function: 'processAuditData_productTitleHumanisation',
      tags: result5.metadata.tags,
      tokens: result5.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'processAuditData_productTitleHumanisation', success: false, error: error.message });
  }

  // Continue with remaining tests
  await testRemainingFunctions(results);

  // Final Dashboard Summary
  console.log('\n📊 DASHBOARD SUMMARY');
  console.log('=' .repeat(80));
  console.log('🎯 These are the EXACT tags you will see on your LiteLLM dashboard:');

  results.filter(r => r.success).forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.function}:`);
    console.log(`   🏷️  Tags: [${result.tags.join(', ')}]`);
    console.log(`   💰 Tokens: ${result.tokens}`);
  });

  console.log('\n🔍 TAG BREAKDOWN:');
  console.log('   🔧 Internal Tags (Auto-added): Jeff, ScrapeGPT, GetChatGPTResponse');
  console.log('   🏢 Business Tags (Your custom): ClientId:X, FeatureTag, ServiceContext, FunctionContext');

  const totalTokens = results.filter(r => r.success).reduce((sum, r) => sum + (r.tokens || 0), 0);
  console.log(`\n💰 Total Test Cost: ${totalTokens} tokens`);
  console.log(`✅ Successful Tests: ${results.filter(r => r.success).length}/${results.length}`);

  return results;
}

async function testRemainingFunctions(results) {
  // Test 6: BSR Category Using GPT
  console.log('\n6️⃣  TESTING: processAuditData (BSR Category Using GPT)');
  console.log('📝 Sample Data: Category Detection via GPT');
  console.log('-'.repeat(50));
  
  try {
    const result6 = await getChatGPTResponse(
      'Categorize product using GPT when BSR category is not available',
      'Wireless Bluetooth Gaming Headset with RGB Lighting',
      1,
      {
        functionName: "processAuditData",
        customTags: [
          `ClientId:1`,
          `BSRCategoryUsingGPT`,
          `AmazonAudit`,
          `ProcessAuditData`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result6.metadata.tags, null, 2));
    console.log('💰 Cost:', result6.total_tokens, 'tokens');
    console.log('⚡ Response:', result6.message.substring(0, 100) + '...');
    
    results.push({
      function: 'processAuditData_bsrCategoryUsingGpt',
      tags: result6.metadata.tags,
      tokens: result6.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'processAuditData_bsrCategoryUsingGpt', success: false, error: error.message });
  }

  // Test 7: Case Studies
  console.log('\n7️⃣  TESTING: processAuditData (Case Studies)');
  console.log('📝 Sample Data: Case Study Generation');
  console.log('-'.repeat(50));
  
  try {
    const result7 = await getChatGPTResponse(
      'Generate case studies for audit reports',
      JSON.stringify({
        productCategory: "Electronics",
        productTitle: "Wireless Bluetooth Headphones"
      }),
      1,
      {
        functionName: "processAuditData",
        customTags: [
          `ClientId:1`,
          `CaseStudies`,
          `AmazonAudit`,
          `ProcessAuditData`,
        ],
      }
    );
    
    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result7.metadata.tags, null, 2));
    console.log('💰 Cost:', result7.total_tokens, 'tokens');
    console.log('⚡ Response:', result7.message.substring(0, 100) + '...');
    
    results.push({
      function: 'processAuditData_caseStudies',
      tags: result7.metadata.tags,
      tokens: result7.total_tokens,
      success: true
    });
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'processAuditData_caseStudies', success: false, error: error.message });
  }

  // Test 8: fetchCompAmazonData
  console.log('\n8️⃣  TESTING: fetchCompAmazonData');
  console.log('📝 Sample Data: Competition Search Keywords');
  console.log('-'.repeat(50));

  try {
    const result8 = await getChatGPTResponse(
      'Generate search keywords for competitor analysis',
      JSON.stringify({
        productTitle: "Wireless Bluetooth Headphones",
        description: "Premium sound quality with noise cancellation",
        price: 99.99
      }),
      1,
      {
        functionName: "fetchCompAmazonData",
        customTags: [
          `ClientId:1`,
          `CompSearchKeyword`,
          `CompetitionData`,
          `FetchCompAmazonData`,
        ],
      }
    );

    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result8.metadata.tags, null, 2));
    console.log('💰 Cost:', result8.total_tokens, 'tokens');
    console.log('⚡ Response:', result8.message.substring(0, 100) + '...');

    results.push({
      function: 'fetchCompAmazonData',
      tags: result8.metadata.tags,
      tokens: result8.total_tokens,
      success: true
    });

  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'fetchCompAmazonData', success: false, error: error.message });
  }

  // Test 9: processCompetitionData (Company Name Humanisation)
  console.log('\n9️⃣  TESTING: processCompetitionData (Company Name Humanisation)');
  console.log('📝 Sample Data: Competitor Company Name');
  console.log('-'.repeat(50));

  try {
    const result9 = await getChatGPTResponse(
      'Humanize competitor company names',
      'SONY ELECTRONICS CORPORATION LTD.',
      1,
      {
        functionName: "processCompetitionData",
        customTags: [
          `ClientId:1`,
          `CompetitorCompanyNameHumanisation`,
          `CompetitionData`,
          `ProcessCompetitionData`,
        ],
      }
    );

    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result9.metadata.tags, null, 2));
    console.log('💰 Cost:', result9.total_tokens, 'tokens');
    console.log('⚡ Response:', result9.message.substring(0, 100) + '...');

    results.push({
      function: 'processCompetitionData_companyNameHumanisation',
      tags: result9.metadata.tags,
      tokens: result9.total_tokens,
      success: true
    });

  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'processCompetitionData_companyNameHumanisation', success: false, error: error.message });
  }

  // Test 10: processCompetitionData (Product Title Humanisation)
  console.log('\n🔟 TESTING: processCompetitionData (Product Title Humanisation)');
  console.log('📝 Sample Data: Competitor Product Title');
  console.log('-'.repeat(50));

  try {
    const result10 = await getChatGPTResponse(
      'Humanize competitor product titles',
      'SONY WH-1000XM4 WIRELESS BLUETOOTH NOISE CANCELING OVER-EAR HEADPHONES',
      1,
      {
        functionName: "processCompetitionData",
        customTags: [
          `ClientId:1`,
          `CompetitorProductTitleHumanisation`,
          `CompetitionData`,
          `ProcessCompetitionData`,
        ],
      }
    );

    console.log('🏷️  DASHBOARD TAGS:', JSON.stringify(result10.metadata.tags, null, 2));
    console.log('💰 Cost:', result10.total_tokens, 'tokens');
    console.log('⚡ Response:', result10.message.substring(0, 100) + '...');

    results.push({
      function: 'processCompetitionData_productTitleHumanisation',
      tags: result10.metadata.tags,
      tokens: result10.total_tokens,
      success: true
    });

  } catch (error) {
    console.log('❌ Error:', error.message);
    results.push({ function: 'processCompetitionData_productTitleHumanisation', success: false, error: error.message });
  }
}

if (require.main === module) {
  testDashboardTags().catch(console.error);
}

module.exports = { testDashboardTags };

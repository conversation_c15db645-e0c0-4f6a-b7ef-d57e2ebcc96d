/**
 * Test the fixed model responses with proper max_tokens
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

async function testFixedModels() {
  console.log('🧪 Testing Fixed Model Responses');
  console.log('=' .repeat(50));
  
  const models = [
    { id: 'gpt-4o', maxTokens: 256 },
    { id: 'gemini/gemini-2.5-flash', maxTokens: 512 },
    { id: 'grok-3-mini', maxTokens: 1000 }
  ];
  
  const testPrompt = {
    system: 'You are a helpful assistant. Please provide a detailed response about what you are.',
    user: 'Explain what AI model you are in 2-3 sentences.'
  };
  
  const results = [];
  
  for (const model of models) {
    console.log(`\n🤖 Testing ${model.id} with max_tokens: ${model.maxTokens}`);
    console.log('-'.repeat(50));
    
    try {
      const startTime = Date.now();
      const result = await getChatGPTResponse(
        testPrompt.system,
        testPrompt.user,
        1,
        {
          model: model.id,
          temperature: 0.7,
          max_tokens: model.maxTokens,
          customTags: [`test:fixed`, `model:${model.id}`]
        }
      );
      const endTime = Date.now();
      
      console.log(`✅ Success (${endTime - startTime}ms):`);
      console.log(`   Message: "${result.message}"`);
      console.log(`   Prompt tokens: ${result.prompt_tokens}`);
      console.log(`   Completion tokens: ${result.completion_tokens}`);
      console.log(`   Reasoning tokens: ${result.completion_tokens_details?.reasoning_tokens || 0}`);
      console.log(`   Total tokens: ${result.total_tokens}`);
      
      // Check if message is empty
      if (!result.message || result.message.trim() === '') {
        console.log(`   ⚠️  Still empty message!`);
      } else {
        console.log(`   ✅ Message received successfully!`);
      }
      
      results.push({
        model: model.id,
        maxTokens: model.maxTokens,
        success: true,
        message: result.message,
        promptTokens: result.prompt_tokens,
        completionTokens: result.completion_tokens,
        reasoningTokens: result.completion_tokens_details?.reasoning_tokens || 0,
        totalTokens: result.total_tokens,
        responseTime: endTime - startTime
      });
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      results.push({
        model: model.id,
        maxTokens: model.maxTokens,
        success: false,
        error: error.message
      });
    }
  }
  
  // Generate CSV preview
  console.log('\n📊 CSV Preview:');
  console.log('=' .repeat(50));
  console.log('Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens');
  
  results.forEach(result => {
    if (result.success) {
      const promptUsed = `"${testPrompt.system} | ${testPrompt.user}"`;
      console.log(`getChatGPTResponse,${result.model},${promptUsed},${result.promptTokens},${result.completionTokens},${result.reasoningTokens}`);
    }
  });
  
  return results;
}

async function main() {
  const results = await testFixedModels();
  
  console.log('\n🎯 Summary:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎉 Working models with proper responses:');
    successful.forEach(result => {
      const hasMessage = result.message && result.message.trim() !== '';
      console.log(`   ${result.model}: ${hasMessage ? '✅' : '❌'} ${result.totalTokens} tokens (${result.reasoningTokens} reasoning)`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n💥 Failed models:');
    failed.forEach(result => {
      console.log(`   ${result.model}: ${result.error}`);
    });
  }
  
  console.log('\n✅ Ready to run full testing script with fixed token limits!');
}

if (require.main === module) {
  main();
}

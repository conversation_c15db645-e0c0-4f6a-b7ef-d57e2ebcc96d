/**
 * Test only the humanization functions to verify they work
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');
const fs = require('fs');

// Safe wrapper functions
async function safeHumanizeCompanyName(companyName, clientId) {
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/CompanyNameHumanizationPrompt.md",
      "utf8"
    );
    return await getChatGPTResponse(
      system_prompt,
      companyName,
      clientId,
      {
        model: 'gpt-4o',
        temperature: 0.7,
        max_tokens: 256,
        customTags: ['test:humanizeCompanyName']
      }
    );
  } catch (error) {
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

async function safeHumanizeProductTitle(productTitle, clientId) {
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/ProductNameHumanizationPrompt.md",
      "utf8"
    );
    return await getChatGPTResponse(
      system_prompt,
      productTitle,
      clientId,
      {
        model: 'gpt-4o',
        temperature: 0.7,
        max_tokens: 256,
        customTags: ['test:humanizeProductTitle']
      }
    );
  } catch (error) {
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

async function testHumanizationFunctions() {
  console.log('🧪 Testing Humanization Functions');
  console.log('=' .repeat(40));
  
  const testCompanyNames = [
    'NIKE, INC.',
    'Apple Computer Corp.',
    'Amazon.com LLC'
  ];
  
  const testProductTitles = [
    'Apple iPhone 15 Pro Max 256GB Natural Titanium Unlocked Smartphone with Advanced Camera System',
    'Samsung 65-Inch 4K Ultra HD Smart LED TV with HDR and Alexa Built-in Model UN65TU8000'
  ];
  
  const results = [];
  
  // Test company name humanization
  console.log('\n📝 Testing Company Name Humanization:');
  for (const companyName of testCompanyNames) {
    try {
      console.log(`   Testing: ${companyName}`);
      const startTime = Date.now();
      const result = await safeHumanizeCompanyName(companyName, 1);
      const endTime = Date.now();
      
      console.log(`   ✅ Success: "${companyName}" → "${result.message}"`);
      console.log(`   🎫 Tokens: ${result.total_tokens} (${endTime - startTime}ms)`);
      
      results.push({
        function: 'humanizeCompanyName',
        input: companyName,
        output: result.message,
        tokens: result.total_tokens,
        responseTime: endTime - startTime,
        success: true
      });
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      results.push({
        function: 'humanizeCompanyName',
        input: companyName,
        error: error.message,
        success: false
      });
    }
  }
  
  // Test product title humanization
  console.log('\n📱 Testing Product Title Humanization:');
  for (const productTitle of testProductTitles) {
    try {
      console.log(`   Testing: ${productTitle.substring(0, 50)}...`);
      const startTime = Date.now();
      const result = await safeHumanizeProductTitle(productTitle, 1);
      const endTime = Date.now();
      
      console.log(`   ✅ Success: "${result.message}"`);
      console.log(`   🎫 Tokens: ${result.total_tokens} (${endTime - startTime}ms)`);
      
      results.push({
        function: 'humanizeProductTitle',
        input: productTitle,
        output: result.message,
        tokens: result.total_tokens,
        responseTime: endTime - startTime,
        success: true
      });
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      results.push({
        function: 'humanizeProductTitle',
        input: productTitle,
        error: error.message,
        success: false
      });
    }
  }
  
  // Generate CSV for successful tests
  console.log('\n📊 Generating CSV...');
  const csvData = [];
  csvData.push(['Function Name', 'Prompt Used', 'Input Tokens', 'Output Tokens', 'Reasoning Tokens']);
  
  results.filter(r => r.success).forEach(result => {
    const promptUsed = `${result.function}: ${result.input.substring(0, 50)}...`;
    csvData.push([
      result.function,
      `"${promptUsed}"`,
      'N/A', // We don't have breakdown from the response
      'N/A',
      0
    ]);
  });
  
  const csvContent = csvData.map(row => row.join(',')).join('\n');
  console.log('\n📋 CSV Preview:');
  csvData.slice(0, 4).forEach(row => {
    console.log(row.join(' | '));
  });
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  const totalTokens = results.filter(r => r.success).reduce((sum, r) => sum + (r.tokens || 0), 0);
  
  console.log('\n📈 Summary:');
  console.log(`   Tests: ${successful}/${total} successful`);
  console.log(`   Total tokens: ${totalTokens}`);
  console.log(`   Success rate: ${((successful/total) * 100).toFixed(1)}%`);
  
  return results;
}

if (require.main === module) {
  testHumanizationFunctions().catch(console.error);
}

module.exports = {
  safeHumanizeCompanyName,
  safeHumanizeProductTitle,
  testHumanizationFunctions
};

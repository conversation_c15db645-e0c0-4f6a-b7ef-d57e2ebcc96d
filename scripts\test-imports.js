/**
 * Test script to verify all imports work correctly
 */

require('dotenv').config();

console.log('🔍 Testing imports for LiteLLM testing script...');

try {
  // Test basic modules
  console.log('✅ axios imported');
  const axios = require('axios');
  
  console.log('✅ fs imported');
  const fs = require('fs');
  
  console.log('✅ path imported');
  const path = require('path');

  // Test Jeff AI service imports
  try {
    console.log('🧪 Testing Jeff AI service imports...');
    
    // Test scrapeGPT services
    const { getChatGPTResponse, getKeyword, humanizeCompanyName, humanizeProductTitle } = require('../src/services/scrapeGPT/request');
    console.log('✅ scrapeGPT/request services imported');
    
    const { completionFactory } = require('../src/services/scrapeGPT/factory');
    console.log('✅ scrapeGPT/factory imported');
    
    // Test MultiProviderAI
    const { MultiProviderAI } = require('../src/services/ai/multiProviderAI');
    console.log('✅ MultiProviderAI imported');
    
    // Test spike services
    const callFeature = require('../src/services/spike/callFeature');
    console.log('✅ spike/callFeature imported');
    
    const { runPromptChain, runLexAnalysisChain } = require('../src/services/spike/runPromptChain');
    console.log('✅ spike/runPromptChain imported');
    
    // Test assistant
    const { run: assistantRun } = require('../src/services/scrapeGPT/assistant');
    console.log('✅ scrapeGPT/assistant imported');
    
    console.log('\n🎉 All imports successful!');
    console.log('📋 Available functions:');
    console.log('   - getChatGPTResponse');
    console.log('   - getKeyword');
    console.log('   - humanizeCompanyName');
    console.log('   - humanizeProductTitle');
    console.log('   - completionFactory');
    console.log('   - MultiProviderAI');
    console.log('   - callFeature');
    console.log('   - runPromptChain');
    console.log('   - runLexAnalysisChain');
    console.log('   - assistantRun');
    
    // Test environment variables
    console.log('\n🔧 Environment check:');
    console.log(`   LITELLM_API_KEY: ${process.env.LITELLM_API_KEY ? '✅ Set' : '❌ Missing'}`);
    console.log(`   OPENAI_MODEL_ID: ${process.env.OPENAI_MODEL_ID ? '✅ Set' : '❌ Missing'}`);
    console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
    
  } catch (error) {
    console.error('❌ Error importing Jeff AI services:', error.message);
    console.error('   This might be expected if running outside the Jeff environment');
  }
  
} catch (error) {
  console.error('❌ Error with basic imports:', error.message);
}

console.log('\n✨ Import test completed!');

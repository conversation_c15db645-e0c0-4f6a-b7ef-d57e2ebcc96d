/**
 * Test the increased token limits (2x) for all models
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

async function testIncreasedTokens() {
  console.log('🧪 Testing Increased Token Limits (2x)');
  console.log('=' .repeat(50));
  
  const models = [
    { id: 'gpt-4o', expectedMaxTokens: 512 },
    { id: 'gemini/gemini-2.5-flash', expectedMaxTokens: 1024 },
    { id: 'grok-3-mini', expectedMaxTokens: 2000 }
  ];
  
  const testPrompt = {
    system: 'You are a helpful assistant. Please provide a very detailed, comprehensive response about the topic. Include multiple paragraphs, examples, and thorough explanations.',
    user: 'Explain artificial intelligence, machine learning, and deep learning in great detail. Include their differences, applications, and future prospects. Be very thorough and comprehensive.'
  };
  
  const results = [];
  
  for (const model of models) {
    console.log(`\n🤖 Testing ${model.id} with max_tokens: ${model.expectedMaxTokens}`);
    console.log('-'.repeat(60));
    
    try {
      // Determine max_tokens based on model type (same logic as in the script)
      let maxTokens = 512; // Doubled from 256
      if (model.id.toLowerCase().includes('grok')) {
        maxTokens = 2000; // Doubled from 1000
      } else if (model.id.toLowerCase().includes('gemini')) {
        maxTokens = 1024; // Doubled from 512
      }
      
      console.log(`   Using max_tokens: ${maxTokens}`);
      
      const startTime = Date.now();
      const result = await getChatGPTResponse(
        testPrompt.system,
        testPrompt.user,
        1,
        {
          model: model.id,
          temperature: 0.7,
          max_tokens: maxTokens,
          customTags: [`test:increasedTokens`, `model:${model.id}`]
        }
      );
      const endTime = Date.now();
      
      console.log(`✅ Success (${endTime - startTime}ms):`);
      console.log(`   Response length: ${result.message?.length || 0} characters`);
      console.log(`   Prompt tokens: ${result.prompt_tokens}`);
      console.log(`   Completion tokens: ${result.completion_tokens}`);
      console.log(`   Reasoning tokens: ${result.completion_tokens_details?.reasoning_tokens || 0}`);
      console.log(`   Total tokens: ${result.total_tokens}`);
      
      // Show first 200 characters of response
      const responsePreview = result.message?.substring(0, 200) + '...';
      console.log(`   Response preview: "${responsePreview}"`);
      
      // Check if we're getting more complete responses
      const isComplete = result.completion_tokens < maxTokens * 0.9; // Less than 90% of max tokens used
      console.log(`   Response appears complete: ${isComplete ? '✅' : '⚠️  (may be truncated)'}`);
      
      results.push({
        model: model.id,
        maxTokensUsed: maxTokens,
        success: true,
        responseLength: result.message?.length || 0,
        promptTokens: result.prompt_tokens,
        completionTokens: result.completion_tokens,
        reasoningTokens: result.completion_tokens_details?.reasoning_tokens || 0,
        totalTokens: result.total_tokens,
        responseTime: endTime - startTime,
        isComplete
      });
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      results.push({
        model: model.id,
        maxTokensUsed: model.expectedMaxTokens,
        success: false,
        error: error.message
      });
    }
  }
  
  // Generate comparison with old vs new token limits
  console.log('\n📊 Token Limit Comparison:');
  console.log('=' .repeat(50));
  console.log('Model                    | Old Limit | New Limit | Increase');
  console.log('-'.repeat(50));
  console.log('gpt-4o                   |    256    |    512    |   2x');
  console.log('gemini/gemini-2.5-flash  |    512    |   1024    |   2x');
  console.log('grok-3-mini              |   1000    |   2000    |   2x');
  
  // Generate CSV preview with new format
  console.log('\n📋 Updated CSV Format Preview:');
  console.log('=' .repeat(50));
  console.log('Function Name,Model Name,Prompt Used,Response,Input Tokens,Output Tokens,Reasoning Tokens');
  
  results.forEach(result => {
    if (result.success) {
      const promptUsed = `"${testPrompt.system} | ${testPrompt.user}"`;
      const responsePreview = `"Response with ${result.responseLength} characters..."`;
      console.log(`getChatGPTResponse,${result.model},${promptUsed},${responsePreview},${result.promptTokens},${result.completionTokens},${result.reasoningTokens}`);
    }
  });
  
  return results;
}

async function main() {
  const results = await testIncreasedTokens();
  
  console.log('\n🎯 Summary:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎉 Token usage with increased limits:');
    successful.forEach(result => {
      const efficiency = Math.round((result.completionTokens / result.maxTokensUsed) * 100);
      console.log(`   ${result.model}:`);
      console.log(`     Max tokens: ${result.maxTokensUsed}`);
      console.log(`     Used tokens: ${result.completionTokens} (${efficiency}% of limit)`);
      console.log(`     Response length: ${result.responseLength} chars`);
      console.log(`     Complete: ${result.isComplete ? '✅' : '⚠️'}`);
    });
    
    const avgResponseLength = Math.round(successful.reduce((sum, r) => sum + r.responseLength, 0) / successful.length);
    console.log(`\n📈 Average response length: ${avgResponseLength} characters`);
  }
  
  console.log('\n✅ Ready to run full testing script with 2x increased token limits!');
  console.log('   - More complete responses expected');
  console.log('   - Better model comparison data');
  console.log('   - Full responses in CSV output');
}

if (require.main === module) {
  main();
}

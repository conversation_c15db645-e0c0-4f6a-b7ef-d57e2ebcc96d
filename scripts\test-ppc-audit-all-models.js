/**
 * Test PPC Audit with ALL models to verify it's working
 */

require('dotenv').config();
const { getChatGPTResponse } = require('../src/services/scrapeGPT/request');

async function testPPCAuditAllModels() {
  console.log('🧪 Testing PPC Audit with ALL Models');
  console.log('=' .repeat(50));
  
  const models = [
    'gpt-4o',
    'gpt-4.1',
    'gpt-4.1-mini',
    'gemini/gemini-2.5-flash',
    'gemini/gemini-2.5-pro',
    'grok-3-mini'
  ];
  
  const ppcAuditData = {
    brandName: 'Nike',
    productTitle: 'Nike Air Max 270 React Bauhaus Running Shoes Men\'s Size 10.5 Black White'
  };
  
  const results = [];
  
  for (const modelId of models) {
    console.log(`\n🤖 Testing PPC Audit with ${modelId}`);
    console.log('-'.repeat(60));
    
    try {
      // Adjust max_tokens based on model type (2x increase)
      let maxTokens = 512; // Doubled from 256
      if (modelId.toLowerCase().includes('grok')) {
        maxTokens = 2000; // Doubled from 1000
      } else if (modelId.toLowerCase().includes('gemini')) {
        maxTokens = 1024; // Doubled from 512
      }
      
      console.log(`   Using max_tokens: ${maxTokens}`);
      
      const factoryPrompt = `You are a completion factory. Process the following request based on the factory type.\n\nFactory Type: ppcAudit\nData: ${JSON.stringify(ppcAuditData)}`;
      
      const startTime = Date.now();
      const result = await getChatGPTResponse(
        'You are a completion factory. Process the following request based on the factory type and provide the appropriate response.',
        factoryPrompt,
        1,
        {
          model: modelId,
          temperature: 0.7,
          max_tokens: maxTokens,
          customTags: [`test:ppcAudit`, `model:${modelId}`]
        }
      );
      const endTime = Date.now();
      
      console.log(`✅ Success (${endTime - startTime}ms):`);
      console.log(`   Response length: ${result.message?.length || 0} characters`);
      console.log(`   Prompt tokens: ${result.prompt_tokens}`);
      console.log(`   Completion tokens: ${result.completion_tokens}`);
      console.log(`   Reasoning tokens: ${result.completion_tokens_details?.reasoning_tokens || 0}`);
      console.log(`   Total tokens: ${result.total_tokens}`);
      
      // Show first 200 characters of response
      const responsePreview = result.message?.substring(0, 200) + '...';
      console.log(`   Response preview: "${responsePreview}"`);
      
      results.push({
        model: modelId,
        success: true,
        responseLength: result.message?.length || 0,
        promptTokens: result.prompt_tokens,
        completionTokens: result.completion_tokens,
        reasoningTokens: result.completion_tokens_details?.reasoning_tokens || 0,
        totalTokens: result.total_tokens,
        responseTime: endTime - startTime,
        response: result.message || ''
      });
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      results.push({
        model: modelId,
        success: false,
        error: error.message
      });
    }
  }
  
  // Generate CSV preview showing PPC Audit results
  console.log('\n📋 PPC Audit CSV Preview:');
  console.log('=' .repeat(50));
  console.log('Function Name,Model Name,Prompt Used,Response,Input Tokens,Output Tokens,Reasoning Tokens');
  
  results.forEach(result => {
    if (result.success) {
      const promptUsed = `"You are a completion factory... | Factory Type: ppcAudit | Data: ${JSON.stringify(ppcAuditData)}"`;
      const responsePreview = `"${result.response.substring(0, 100).replace(/"/g, '""')}..."`;
      console.log(`completionFactory,${result.model},${promptUsed},${responsePreview},${result.promptTokens},${result.completionTokens},${result.reasoningTokens}`);
    }
  });
  
  return results;
}

async function main() {
  const results = await testPPCAuditAllModels();
  
  console.log('\n🎯 PPC Audit Testing Summary:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎉 PPC Audit results by model:');
    successful.forEach(result => {
      console.log(`   ${result.model}:`);
      console.log(`     Response length: ${result.responseLength} chars`);
      console.log(`     Total tokens: ${result.totalTokens}`);
      console.log(`     Reasoning tokens: ${result.reasoningTokens}`);
      console.log(`     Response time: ${result.responseTime}ms`);
    });
    
    const avgResponseLength = Math.round(successful.reduce((sum, r) => sum + r.responseLength, 0) / successful.length);
    const avgTokens = Math.round(successful.reduce((sum, r) => sum + r.totalTokens, 0) / successful.length);
    console.log(`\n📈 Averages:`);
    console.log(`   Response length: ${avgResponseLength} characters`);
    console.log(`   Total tokens: ${avgTokens}`);
  }
  
  console.log('\n✅ PPC Audit is now tested with ALL models in the main script!');
  console.log('   - Each model will process PPC audit requests');
  console.log('   - Results will appear in CSV with model comparison');
  console.log('   - Full responses included (not truncated)');
}

if (require.main === module) {
  main();
}

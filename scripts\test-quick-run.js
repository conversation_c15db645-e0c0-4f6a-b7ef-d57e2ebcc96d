/**
 * Quick test of the main script with all models
 */

require('dotenv').config();
const { testGetChatGPTResponse, testHumanizationFunctions, fetchAvailableModels } = require('./litellm-testing-script');

// Mock testResults global
global.testResults = {
  timestamp: new Date().toISOString(),
  availableModels: [],
  functionTests: {},
  summary: {
    totalTests: 0,
    successfulTests: 0,
    failedTests: 0,
    totalTokensUsed: 0,
    totalCost: 0
  }
};

async function quickTest() {
  console.log('🚀 Quick Test of All Models on All Functions');
  console.log('=' .repeat(60));
  
  try {
    // Fetch available models
    console.log('\n📋 Fetching available models...');
    const models = await fetchAvailableModels();
    global.testResults.availableModels = models;
    
    console.log(`✅ Found ${models.length} models: ${models.map(m => m.id).join(', ')}`);
    
    // Test getChatGPTResponse with all models (limited test cases)
    console.log('\n🧪 Testing getChatGPTResponse with ALL models...');
    await testGetChatGPTResponse();
    
    console.log(`\n📊 Results so far:`);
    console.log(`   Total tests: ${global.testResults.summary.totalTests}`);
    console.log(`   Successful: ${global.testResults.summary.successfulTests}`);
    console.log(`   Failed: ${global.testResults.summary.failedTests}`);
    console.log(`   Total tokens: ${global.testResults.summary.totalTokensUsed}`);
    
    // Generate CSV preview
    console.log('\n📋 CSV Preview:');
    console.log('Function Name,Model Name,Prompt Used,Input Tokens,Output Tokens,Reasoning Tokens');
    
    const results = global.testResults.functionTests.getChatGPTResponse || [];
    results.slice(0, 6).forEach(result => { // Show first 6 results
      if (result.success) {
        const promptPreview = result.promptUsed.length > 100 ? 
          `"${result.promptUsed.substring(0, 100)}..."` : 
          `"${result.promptUsed}"`;
        
        console.log(`getChatGPTResponse,${result.modelUsed},${promptPreview},${result.tokenUsage.promptTokens},${result.tokenUsage.completionTokens},${result.tokenUsage.reasoning_tokens}`);
      }
    });
    
    // Test one humanization function with all models (limited)
    console.log('\n🧪 Testing humanization with ALL models (limited)...');
    
    // Temporarily reduce sample data for quick test
    const originalCompanyNames = require('./litellm-testing-script').sampleData?.companyNames || ['NIKE, INC.'];
    const originalProductTitles = require('./litellm-testing-script').sampleData?.productTitles || ['Nike Air Max 270 Running Shoes'];
    
    // Override sample data for quick test
    global.sampleData = {
      companyNames: ['NIKE, INC.'], // Just one company
      productTitles: ['Nike Air Max 270 Running Shoes'], // Just one product
      amazonProduct: {
        title: 'Nike Air Max 270 Running Shoes',
        description: 'Comfortable athletic footwear with Air Max technology'
      }
    };
    
    await testHumanizationFunctions();
    
    console.log(`\n📊 Final Results:`);
    console.log(`   Total tests: ${global.testResults.summary.totalTests}`);
    console.log(`   Successful: ${global.testResults.summary.successfulTests}`);
    console.log(`   Failed: ${global.testResults.summary.failedTests}`);
    console.log(`   Total tokens: ${global.testResults.summary.totalTokensUsed}`);
    
    // Show model breakdown
    const allResults = [
      ...(global.testResults.functionTests.getChatGPTResponse || []),
      ...(global.testResults.functionTests.humanizationFunctions || [])
    ];
    
    const modelStats = {};
    allResults.forEach(result => {
      if (result.success && result.modelUsed) {
        if (!modelStats[result.modelUsed]) {
          modelStats[result.modelUsed] = { tests: 0, tokens: 0, reasoning: 0 };
        }
        modelStats[result.modelUsed].tests++;
        modelStats[result.modelUsed].tokens += result.tokenUsage?.totalTokens || 0;
        modelStats[result.modelUsed].reasoning += result.tokenUsage?.reasoning_tokens || 0;
      }
    });
    
    console.log('\n🎯 Model Performance:');
    Object.entries(modelStats).forEach(([model, stats]) => {
      console.log(`   ${model}: ${stats.tests} tests, ${stats.tokens} tokens (${stats.reasoning} reasoning)`);
    });
    
    return {
      totalTests: global.testResults.summary.totalTests,
      successful: global.testResults.summary.successfulTests,
      failed: global.testResults.summary.failedTests,
      totalTokens: global.testResults.summary.totalTokensUsed,
      modelStats
    };
    
  } catch (error) {
    console.error('\n❌ Quick test failed:', error.message);
    console.error(error.stack);
    return null;
  }
}

async function main() {
  const results = await quickTest();
  
  if (results) {
    console.log('\n🎉 Quick Test Complete!');
    console.log(`✅ ${results.successful}/${results.totalTests} tests passed`);
    console.log(`🎫 ${results.totalTokens} total tokens used`);
    console.log(`🤖 ${Object.keys(results.modelStats).length} models tested`);
    
    console.log('\n✅ Ready to run full testing script with:');
    console.log('   - All models working (GPT, Gemini, Grok)');
    console.log('   - Proper token limits for each model type');
    console.log('   - Correct reasoning token extraction');
    console.log('   - Full CSV output with all results');
  }
}

if (require.main === module) {
  main();
}

/**
 * Test the updated features: full prompts, multiple models, Grok/Gemini support
 */

require('dotenv').config();
const { fetchAvailableModels, testMultiProviderAI, generateTestReport } = require('./litellm-testing-script');

async function testUpdatedFeatures() {
  console.log('🧪 Testing Updated LiteLLM Script Features');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: Check available models
    console.log('\n📋 Test 1: Checking available models...');
    const models = await fetchAvailableModels();
    
    console.log(`✅ Found ${models.length} models:`);
    models.forEach(model => {
      const isSpecial = model.id.toLowerCase().includes('grok') || 
                       model.id.toLowerCase().includes('gemini') || 
                       model.id.toLowerCase().includes('claude');
      console.log(`   ${isSpecial ? '🎯' : '  '} ${model.id}`);
    });
    
    // Check for specific models
    const hasGrok = models.some(m => m.id.toLowerCase().includes('grok'));
    const hasGemini = models.some(m => m.id.toLowerCase().includes('gemini'));
    const hasClaude = models.some(m => m.id.toLowerCase().includes('claude'));
    
    console.log('\n🔍 Model availability:');
    console.log(`   Grok models: ${hasGrok ? '✅ Available' : '❌ Not found'}`);
    console.log(`   Gemini models: ${hasGemini ? '✅ Available' : '❌ Not found'}`);
    console.log(`   Claude models: ${hasClaude ? '✅ Available' : '❌ Not found'}`);
    
    // Test 2: Test prompt collection
    console.log('\n📝 Test 2: Testing full prompt collection...');
    const fs = require('fs');
    
    try {
      const companyPrompt = fs.readFileSync("src/services/scrapeGPT/CompanyNameHumanizationPrompt.md", "utf8");
      const productPrompt = fs.readFileSync("src/services/scrapeGPT/ProductNameHumanizationPrompt.md", "utf8");
      const amazonPrompt = fs.readFileSync("src/services/scrapeGPT/AmazonPrompt.md", "utf8");
      
      console.log(`✅ Company prompt loaded: ${companyPrompt.length} characters`);
      console.log(`✅ Product prompt loaded: ${productPrompt.length} characters`);
      console.log(`✅ Amazon prompt loaded: ${amazonPrompt.length} characters`);
      
      // Show first few lines of each prompt
      console.log('\n📖 Prompt previews:');
      console.log(`   Company: "${companyPrompt.split('\n')[0].substring(0, 80)}..."`);
      console.log(`   Product: "${productPrompt.split('\n')[0].substring(0, 80)}..."`);
      console.log(`   Amazon: "${amazonPrompt.split('\n')[0].substring(0, 80)}..."`);
      
    } catch (error) {
      console.log(`❌ Error loading prompts: ${error.message}`);
    }
    
    // Test 3: Test CSV format with mock data
    console.log('\n📊 Test 3: Testing CSV format with full prompts...');
    
    const mockTestResults = {
      timestamp: new Date().toISOString(),
      availableModels: models,
      functionTests: {
        getChatGPTResponse: [
          {
            testCase: 'Full Prompt Test',
            success: true,
            responseTime: 1200,
            tokenUsage: {
              promptTokens: 250,
              completionTokens: 85,
              totalTokens: 335,
              reasoning_tokens: 0
            },
            promptUsed: 'System: You are a helpful AI assistant that analyzes Amazon product data. Please provide detailed insights about the product including category, features, and market positioning. | User: {"title":"Nike Air Max 270 Running Shoes","description":"Comfortable athletic footwear"}',
            modelUsed: 'gpt-4o'
          },
          {
            testCase: 'Gemini Model Test',
            success: true,
            responseTime: 950,
            tokenUsage: {
              promptTokens: 180,
              completionTokens: 92,
              totalTokens: 272,
              reasoning_tokens: 0
            },
            promptUsed: 'System: Analyze the following content for sentiment and key themes. | User: This product is amazing and works perfectly for my needs.',
            modelUsed: 'gemini/gemini-2.5-flash'
          }
        ]
      },
      summary: {
        totalTests: 2,
        successfulTests: 2,
        failedTests: 0,
        totalTokensUsed: 607,
        totalCost: 0.012
      }
    };
    
    // Generate CSV
    const csvData = [];
    csvData.push(['Function Name', 'Model Name', 'Prompt Used', 'Input Tokens', 'Output Tokens', 'Reasoning Tokens']);
    
    Object.entries(mockTestResults.functionTests).forEach(([functionName, tests]) => {
      tests.forEach(test => {
        if (test.success) {
          const promptUsed = test.promptUsed || 'N/A';
          const inputTokens = test.tokenUsage?.promptTokens || 'N/A';
          const outputTokens = test.tokenUsage?.completionTokens || 'N/A';
          const reasoningTokens = test.tokenUsage?.reasoning_tokens || 0;
          const modelName = test.modelUsed || 'gpt-4o';
          
          csvData.push([
            functionName,
            modelName,
            `"${promptUsed.replace(/"/g, '""').replace(/\n/g, ' ').replace(/\r/g, '')}"`,
            inputTokens,
            outputTokens,
            reasoningTokens
          ]);
        }
      });
    });
    
    console.log('✅ CSV generation successful!');
    console.log('\n📋 CSV Preview (with full prompts):');
    csvData.slice(0, 3).forEach((row, index) => {
      if (index === 0) {
        console.log(`Header: ${row.join(' | ')}`);
      } else {
        const shortPrompt = row[2].length > 100 ? row[2].substring(0, 100) + '..."' : row[2];
        console.log(`Row ${index}: ${row[0]} | ${row[1]} | ${shortPrompt} | ${row[3]} | ${row[4]} | ${row[5]}`);
      }
    });
    
    // Test 4: Model rotation test
    console.log('\n🔄 Test 4: Testing model rotation...');
    const testModels = ['gpt-4o', 'gpt-4.1-jeff', 'gemini/gemini-2.5-flash'];
    const testCases = ['Test 1', 'Test 2', 'Test 3', 'Test 4', 'Test 5'];
    
    console.log('Model rotation pattern:');
    testCases.forEach((testCase, i) => {
      const selectedModel = testModels[i % testModels.length];
      console.log(`   ${testCase}: ${selectedModel}`);
    });
    
    console.log('\n🎉 All updated features tested successfully!');
    
    return {
      modelsFound: models.length,
      hasGrok,
      hasGemini,
      hasClaude,
      promptsLoaded: true,
      csvFormatCorrect: true
    };
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
    return null;
  }
}

async function main() {
  const results = await testUpdatedFeatures();
  
  if (results) {
    console.log('\n📊 Test Summary:');
    console.log(`   Models found: ${results.modelsFound}`);
    console.log(`   Grok available: ${results.hasGrok ? 'Yes' : 'No'}`);
    console.log(`   Gemini available: ${results.hasGemini ? 'Yes' : 'No'}`);
    console.log(`   Claude available: ${results.hasClaude ? 'Yes' : 'No'}`);
    console.log(`   Full prompts: ${results.promptsLoaded ? 'Working' : 'Failed'}`);
    console.log(`   CSV format: ${results.csvFormatCorrect ? 'Correct' : 'Incorrect'}`);
    
    console.log('\n✅ Ready to run the full LiteLLM testing script!');
    console.log('Run: npm run test:litellm');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  testUpdatedFeatures
};

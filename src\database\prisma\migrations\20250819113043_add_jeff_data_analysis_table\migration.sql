-- CreateTable
CREATE TABLE "JeffDataAnalysis" (
    "id" SERIAL NOT NULL,
    "companyName" TEXT NOT NULL,
    "companyNameHumanized" TEXT NOT NULL DEFAULT '',
    "companyWebsite" TEXT NOT NULL DEFAULT '',
    "companySlug" TEXT NOT NULL DEFAULT '',
    "companyId" INTEGER NOT NULL,
    "prospectFirstName" TEXT NOT NULL DEFAULT '',
    "prospectSecondName" TEXT NOT NULL DEFAULT '',
    "prospectJobTitle" TEXT NOT NULL DEFAULT '',
    "prospectEmail" TEXT NOT NULL DEFAULT '',
    "prospectLinkedinUrl" TEXT NOT NULL DEFAULT '',
    "companyLinkedinUrl" TEXT NOT NULL DEFAULT '',
    "productSlug" TEXT NOT NULL DEFAULT '',
    "productAmazonUrl" TEXT NOT NULL DEFAULT '',
    "productTitle" TEXT NOT NULL DEFAULT '',
    "productTitleHumanized" TEXT NOT NULL DEFAULT '',
    "productPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "productAsin" TEXT NOT NULL DEFAULT '',
    "sellerId" TEXT,
    "marketplace" TEXT,
    "sellerUrl" TEXT,
    "url" TEXT NOT NULL,
    "brandName" VARCHAR(255),
    "productTitleRaw" TEXT,
    "description" TEXT,
    "price" DECIMAL(10,2),
    "priceRaw" DOUBLE PRECISION DEFAULT 0.0,
    "priceCurrency" TEXT NOT NULL DEFAULT '$',
    "competitorEmail" TEXT NOT NULL DEFAULT '',
    "competitorProductUrl" TEXT NOT NULL DEFAULT '',
    "competitorAsin" TEXT NOT NULL DEFAULT '',
    "competitorProductTitle" TEXT NOT NULL DEFAULT '',
    "competitorProductTitleHumanized" TEXT NOT NULL DEFAULT '',
    "competitorBrandName" TEXT NOT NULL DEFAULT '',
    "competitorBrandNameHumanized" TEXT NOT NULL DEFAULT '',
    "competitorRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "competitorAnnualRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "prospectRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "revenueDifference" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "revenueDifferenceMonthly" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "revenueDifferenceYearly" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "revenueSource" TEXT NOT NULL DEFAULT '',
    "amazonSearchUrl" TEXT NOT NULL DEFAULT '',
    "bsrCategory" TEXT NOT NULL DEFAULT '',
    "auditWebpageLink" TEXT NOT NULL DEFAULT '',
    "auditPdfLink" TEXT NOT NULL DEFAULT '',
    "encodedAuditUrl" TEXT NOT NULL DEFAULT '',
    "encodedAuditSlug" TEXT NOT NULL DEFAULT '',
    "pageImage" TEXT NOT NULL DEFAULT '',
    "productImage" TEXT NOT NULL DEFAULT '',
    "auditMailImage" TEXT NOT NULL DEFAULT '',
    "mainImageUrl" TEXT,
    "imageCountRaw" INTEGER,
    "videoCountRaw" INTEGER,
    "oneStarReviews" INTEGER NOT NULL DEFAULT 0,
    "twoStarReviews" INTEGER NOT NULL DEFAULT 0,
    "oneAndTwoStarReviews" INTEGER NOT NULL DEFAULT 0,
    "totalRatings" INTEGER NOT NULL DEFAULT 0,
    "numberOfStars" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "numberOfStarsComesAs" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "goalForNumberOfStars" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "goalForNumberOfStarsComesAs" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "numberOf5StarRatingsNeeded" INTEGER NOT NULL DEFAULT 0,
    "minimumLowStarRemoval" INTEGER NOT NULL DEFAULT 0,
    "rating" DECIMAL(3,2),
    "totalReviews" INTEGER,
    "reviewCategory" VARCHAR(100),
    "star5Count" INTEGER,
    "star4Count" INTEGER,
    "star3Count" INTEGER,
    "star2Count" INTEGER,
    "star1Count" INTEGER,
    "salesCountRaw" INTEGER,
    "numberOfOptimizations" INTEGER NOT NULL DEFAULT 0,
    "titleCharCount" INTEGER,
    "titleUnder150Chars" BOOLEAN,
    "outOfStockRaw" BOOLEAN NOT NULL DEFAULT false,
    "brandedKeyword" TEXT NOT NULL DEFAULT '',
    "nonBrandedKeyword" TEXT NOT NULL DEFAULT '',
    "searchKeyword" TEXT NOT NULL DEFAULT '',
    "compKeyPrompt" TEXT NOT NULL DEFAULT '',
    "mainImageOptimization" TEXT NOT NULL DEFAULT '',
    "altTextPresent" BOOLEAN NOT NULL DEFAULT false,
    "altTextOptimization" TEXT NOT NULL DEFAULT '',
    "dataPoint1" TEXT NOT NULL DEFAULT '',
    "painPoint1" TEXT NOT NULL DEFAULT '',
    "topImprovement1" TEXT NOT NULL DEFAULT '',
    "dataPoint2" TEXT NOT NULL DEFAULT '',
    "painPoint2" TEXT NOT NULL DEFAULT '',
    "topImprovement2" TEXT NOT NULL DEFAULT '',
    "dataPoint3" TEXT NOT NULL DEFAULT '',
    "painPoint3" TEXT NOT NULL DEFAULT '',
    "topImprovement3" TEXT NOT NULL DEFAULT '',
    "qualificationStatus" TEXT NOT NULL DEFAULT '',
    "amazonDataStatus" TEXT NOT NULL DEFAULT 'pending',
    "homepageDataStatus" TEXT NOT NULL DEFAULT 'not-found',
    "aboutDataStatus" TEXT NOT NULL DEFAULT 'pending',
    "jobId" INTEGER NOT NULL,
    "jobName" TEXT NOT NULL DEFAULT '',
    "jobStatus" TEXT NOT NULL DEFAULT '',
    "campaignName" TEXT NOT NULL DEFAULT '',
    "inputPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "outputPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "promptTokens" INTEGER NOT NULL DEFAULT 0,
    "completionTokens" INTEGER NOT NULL DEFAULT 0,
    "scraperApiCreditsUsed" INTEGER NOT NULL DEFAULT 0,
    "scrapingBeeCreditsUsed" INTEGER NOT NULL DEFAULT 0,
    "jungleScoutCreditsUsed" INTEGER NOT NULL DEFAULT 0,
    "chatGptInputTokens" INTEGER NOT NULL DEFAULT 0,
    "chatGptOutputTokens" INTEGER NOT NULL DEFAULT 0,
    "scraperApiTotalCost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "scrapingBeeTotalCost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "jungleScoutTotalCost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "chatGptTotalCost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "totalCost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "productDescription" TEXT NOT NULL DEFAULT '',
    "productDescriptionCharCount" INTEGER NOT NULL DEFAULT 0,
    "bulletPoints" JSONB NOT NULL DEFAULT '[]',
    "categoryAndRank" JSONB NOT NULL DEFAULT '[]',
    "secondaryImages" JSONB NOT NULL DEFAULT '[]',
    "imageCount" INTEGER NOT NULL DEFAULT 0,
    "videoCount" INTEGER NOT NULL DEFAULT 0,
    "salesCount" INTEGER NOT NULL DEFAULT 0,
    "outOfStock" BOOLEAN NOT NULL DEFAULT false,
    "aplusContentPresent" BOOLEAN NOT NULL DEFAULT false,
    "allImagesHaveAltText" BOOLEAN NOT NULL DEFAULT false,
    "brandStoryPresent" BOOLEAN NOT NULL DEFAULT false,
    "premiumAplusPresent" BOOLEAN NOT NULL DEFAULT false,
    "aplusContentPresentRaw" BOOLEAN NOT NULL DEFAULT false,
    "premiumAplusPresentRaw" BOOLEAN NOT NULL DEFAULT false,
    "brandStoryPresentRaw" BOOLEAN NOT NULL DEFAULT false,
    "storefrontPresentRaw" BOOLEAN NOT NULL DEFAULT false,
    "storefrontPresent" BOOLEAN NOT NULL DEFAULT false,
    "storefrontUrl" TEXT NOT NULL DEFAULT '',
    "countryCode" TEXT NOT NULL DEFAULT '',
    "ppcAudit" JSONB NOT NULL DEFAULT '{}',
    "sponsoredCheck" BOOLEAN NOT NULL DEFAULT false,
    "caseStudies" JSONB NOT NULL DEFAULT '{}',
    "bulletPointsRaw" JSONB DEFAULT '{}',
    "categoriesAndRanks" JSONB DEFAULT '{}',
    "secondaryImagesRaw" JSONB DEFAULT '{}',
    "brandStoryImages" JSONB DEFAULT '{}',
    "fullJsonData" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JeffDataAnalysis_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_jobId_idx" ON "JeffDataAnalysis"("jobId");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_companyId_idx" ON "JeffDataAnalysis"("companyId");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_productSlug_idx" ON "JeffDataAnalysis"("productSlug");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_companySlug_idx" ON "JeffDataAnalysis"("companySlug");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_createdAt_idx" ON "JeffDataAnalysis"("createdAt");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_amazonDataStatus_idx" ON "JeffDataAnalysis"("amazonDataStatus");

-- CreateIndex
CREATE INDEX "JeffDataAnalysis_qualificationStatus_idx" ON "JeffDataAnalysis"("qualificationStatus");

-- AddForeignKey
ALTER TABLE "JeffDataAnalysis" ADD CONSTRAINT "JeffDataAnalysis_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JeffDataAnalysis" ADD CONSTRAINT "JeffDataAnalysis_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE CASCADE ON UPDATE CASCADE;

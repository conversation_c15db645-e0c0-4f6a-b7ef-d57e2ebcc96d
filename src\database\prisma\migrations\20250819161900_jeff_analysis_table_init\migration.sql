/*
  Warnings:

  - You are about to drop the column `aboutDataStatus` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `allImagesHaveAltText` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `altTextOptimization` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `altTextPresent` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `amazonDataStatus` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `amazonSearchUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `aplusContentPresent` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `auditMailImage` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `auditPdfLink` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `auditWebpageLink` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `brandName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `brandStoryImages` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `brandStoryPresent` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `brandedKeyword` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `bsrCategory` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `bulletPoints` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `campaignName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `caseStudies` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `categoriesAndRanks` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `categoryAndRank` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `chatGptInputTokens` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `chatGptOutputTokens` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `chatGptTotalCost` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `compKeyPrompt` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companyId` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companyLinkedinUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companyName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companyNameHumanized` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companySlug` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `companyWebsite` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorAnnualRevenue` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorAsin` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorBrandName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorBrandNameHumanized` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorEmail` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorProductTitle` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorProductTitleHumanized` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorProductUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `competitorRevenue` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `completionTokens` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `countryCode` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `dataPoint1` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `dataPoint2` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `dataPoint3` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `encodedAuditSlug` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `encodedAuditUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `fullJsonData` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `goalForNumberOfStars` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `goalForNumberOfStarsComesAs` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `homepageDataStatus` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `imageCount` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `inputPrice` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `jobId` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `jobName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `jobStatus` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `jungleScoutCreditsUsed` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `jungleScoutTotalCost` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `mainImageOptimization` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `mainImageUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `minimumLowStarRemoval` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `nonBrandedKeyword` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `numberOf5StarRatingsNeeded` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `numberOfOptimizations` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `numberOfStars` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `numberOfStarsComesAs` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `oneAndTwoStarReviews` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `oneStarReviews` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `outOfStock` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `outputPrice` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `pageImage` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `painPoint1` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `painPoint2` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `painPoint3` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `ppcAudit` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `premiumAplusPresent` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productAmazonUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productAsin` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productDescription` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productDescriptionCharCount` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productImage` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productPrice` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productSlug` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productTitle` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productTitleHumanized` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `promptTokens` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectEmail` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectFirstName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectJobTitle` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectLinkedinUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectRevenue` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `prospectSecondName` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `qualificationStatus` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `revenueDifference` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `revenueDifferenceMonthly` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `revenueDifferenceYearly` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `revenueSource` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `reviewCategory` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `salesCount` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `scraperApiCreditsUsed` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `scraperApiTotalCost` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `scrapingBeeCreditsUsed` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `scrapingBeeTotalCost` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `searchKeyword` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `secondaryImages` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `sponsoredCheck` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `star1Count` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `star2Count` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `star3Count` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `star4Count` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `star5Count` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `storefrontPresent` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `storefrontUrl` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `titleCharCount` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `titleUnder150Chars` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `topImprovement1` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `topImprovement2` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `topImprovement3` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `totalCost` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `totalRatings` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `totalReviews` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `twoStarReviews` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `videoCount` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - Added the required column `company_id` to the `JeffDataAnalysis` table without a default value. This is not possible if the table is not empty.
  - Added the required column `company_name` to the `JeffDataAnalysis` table without a default value. This is not possible if the table is not empty.
  - Added the required column `job_id` to the `JeffDataAnalysis` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "JeffDataAnalysis" DROP CONSTRAINT "JeffDataAnalysis_companyId_fkey";

-- DropForeignKey
ALTER TABLE "JeffDataAnalysis" DROP CONSTRAINT "JeffDataAnalysis_jobId_fkey";

-- DropIndex
DROP INDEX "JeffDataAnalysis_amazonDataStatus_idx";

-- DropIndex
DROP INDEX "JeffDataAnalysis_companyId_idx";

-- DropIndex
DROP INDEX "JeffDataAnalysis_companySlug_idx";

-- DropIndex
DROP INDEX "JeffDataAnalysis_jobId_idx";

-- DropIndex
DROP INDEX "JeffDataAnalysis_productSlug_idx";

-- DropIndex
DROP INDEX "JeffDataAnalysis_qualificationStatus_idx";

-- AlterTable
ALTER TABLE "JeffDataAnalysis" DROP COLUMN "aboutDataStatus",
DROP COLUMN "allImagesHaveAltText",
DROP COLUMN "altTextOptimization",
DROP COLUMN "altTextPresent",
DROP COLUMN "amazonDataStatus",
DROP COLUMN "amazonSearchUrl",
DROP COLUMN "aplusContentPresent",
DROP COLUMN "auditMailImage",
DROP COLUMN "auditPdfLink",
DROP COLUMN "auditWebpageLink",
DROP COLUMN "brandName",
DROP COLUMN "brandStoryImages",
DROP COLUMN "brandStoryPresent",
DROP COLUMN "brandedKeyword",
DROP COLUMN "bsrCategory",
DROP COLUMN "bulletPoints",
DROP COLUMN "campaignName",
DROP COLUMN "caseStudies",
DROP COLUMN "categoriesAndRanks",
DROP COLUMN "categoryAndRank",
DROP COLUMN "chatGptInputTokens",
DROP COLUMN "chatGptOutputTokens",
DROP COLUMN "chatGptTotalCost",
DROP COLUMN "compKeyPrompt",
DROP COLUMN "companyId",
DROP COLUMN "companyLinkedinUrl",
DROP COLUMN "companyName",
DROP COLUMN "companyNameHumanized",
DROP COLUMN "companySlug",
DROP COLUMN "companyWebsite",
DROP COLUMN "competitorAnnualRevenue",
DROP COLUMN "competitorAsin",
DROP COLUMN "competitorBrandName",
DROP COLUMN "competitorBrandNameHumanized",
DROP COLUMN "competitorEmail",
DROP COLUMN "competitorProductTitle",
DROP COLUMN "competitorProductTitleHumanized",
DROP COLUMN "competitorProductUrl",
DROP COLUMN "competitorRevenue",
DROP COLUMN "completionTokens",
DROP COLUMN "countryCode",
DROP COLUMN "dataPoint1",
DROP COLUMN "dataPoint2",
DROP COLUMN "dataPoint3",
DROP COLUMN "encodedAuditSlug",
DROP COLUMN "encodedAuditUrl",
DROP COLUMN "fullJsonData",
DROP COLUMN "goalForNumberOfStars",
DROP COLUMN "goalForNumberOfStarsComesAs",
DROP COLUMN "homepageDataStatus",
DROP COLUMN "imageCount",
DROP COLUMN "inputPrice",
DROP COLUMN "jobId",
DROP COLUMN "jobName",
DROP COLUMN "jobStatus",
DROP COLUMN "jungleScoutCreditsUsed",
DROP COLUMN "jungleScoutTotalCost",
DROP COLUMN "mainImageOptimization",
DROP COLUMN "mainImageUrl",
DROP COLUMN "minimumLowStarRemoval",
DROP COLUMN "nonBrandedKeyword",
DROP COLUMN "numberOf5StarRatingsNeeded",
DROP COLUMN "numberOfOptimizations",
DROP COLUMN "numberOfStars",
DROP COLUMN "numberOfStarsComesAs",
DROP COLUMN "oneAndTwoStarReviews",
DROP COLUMN "oneStarReviews",
DROP COLUMN "outOfStock",
DROP COLUMN "outputPrice",
DROP COLUMN "pageImage",
DROP COLUMN "painPoint1",
DROP COLUMN "painPoint2",
DROP COLUMN "painPoint3",
DROP COLUMN "ppcAudit",
DROP COLUMN "premiumAplusPresent",
DROP COLUMN "productAmazonUrl",
DROP COLUMN "productAsin",
DROP COLUMN "productDescription",
DROP COLUMN "productDescriptionCharCount",
DROP COLUMN "productImage",
DROP COLUMN "productPrice",
DROP COLUMN "productSlug",
DROP COLUMN "productTitle",
DROP COLUMN "productTitleHumanized",
DROP COLUMN "promptTokens",
DROP COLUMN "prospectEmail",
DROP COLUMN "prospectFirstName",
DROP COLUMN "prospectJobTitle",
DROP COLUMN "prospectLinkedinUrl",
DROP COLUMN "prospectRevenue",
DROP COLUMN "prospectSecondName",
DROP COLUMN "qualificationStatus",
DROP COLUMN "revenueDifference",
DROP COLUMN "revenueDifferenceMonthly",
DROP COLUMN "revenueDifferenceYearly",
DROP COLUMN "revenueSource",
DROP COLUMN "reviewCategory",
DROP COLUMN "salesCount",
DROP COLUMN "scraperApiCreditsUsed",
DROP COLUMN "scraperApiTotalCost",
DROP COLUMN "scrapingBeeCreditsUsed",
DROP COLUMN "scrapingBeeTotalCost",
DROP COLUMN "searchKeyword",
DROP COLUMN "secondaryImages",
DROP COLUMN "sponsoredCheck",
DROP COLUMN "star1Count",
DROP COLUMN "star2Count",
DROP COLUMN "star3Count",
DROP COLUMN "star4Count",
DROP COLUMN "star5Count",
DROP COLUMN "storefrontPresent",
DROP COLUMN "storefrontUrl",
DROP COLUMN "titleCharCount",
DROP COLUMN "titleUnder150Chars",
DROP COLUMN "topImprovement1",
DROP COLUMN "topImprovement2",
DROP COLUMN "topImprovement3",
DROP COLUMN "totalCost",
DROP COLUMN "totalRatings",
DROP COLUMN "totalReviews",
DROP COLUMN "twoStarReviews",
DROP COLUMN "videoCount",
ADD COLUMN     "amazon_search_url" TEXT,
ADD COLUMN     "aplus_content_present" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "brand_name" VARCHAR(255),
ADD COLUMN     "brand_story_images" JSONB DEFAULT '{}',
ADD COLUMN     "brand_story_present" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "branded_keyword" VARCHAR(255),
ADD COLUMN     "bullet_points" JSONB DEFAULT '{}',
ADD COLUMN     "campaign_name" VARCHAR(255),
ADD COLUMN     "categories_and_ranks" JSONB DEFAULT '{}',
ADD COLUMN     "comp_key_prompt" TEXT,
ADD COLUMN     "company_id" INTEGER NOT NULL,
ADD COLUMN     "company_linkedin_url" TEXT,
ADD COLUMN     "company_name" VARCHAR(255) NOT NULL,
ADD COLUMN     "company_name_humanized" TEXT,
ADD COLUMN     "company_slug" VARCHAR(255),
ADD COLUMN     "company_website" TEXT,
ADD COLUMN     "competitor_annual_revenue" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "competitor_asin" VARCHAR(50),
ADD COLUMN     "competitor_brand_name" VARCHAR(255),
ADD COLUMN     "competitor_brand_name_humanized" TEXT,
ADD COLUMN     "competitor_product_title" TEXT,
ADD COLUMN     "competitor_product_title_humanized" TEXT,
ADD COLUMN     "competitor_product_url" TEXT,
ADD COLUMN     "competitor_revenue" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "data_point_1" TEXT,
ADD COLUMN     "data_point_2" TEXT,
ADD COLUMN     "data_point_3" TEXT,
ADD COLUMN     "image_count" INTEGER,
ADD COLUMN     "job_id" INTEGER NOT NULL,
ADD COLUMN     "job_name" VARCHAR(255),
ADD COLUMN     "job_status" VARCHAR(100),
ADD COLUMN     "main_image_optimization" TEXT,
ADD COLUMN     "main_image_url" TEXT,
ADD COLUMN     "non_branded_keyword" VARCHAR(255),
ADD COLUMN     "number_of_optimizations" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "out_of_stock" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "pain_point_1" TEXT,
ADD COLUMN     "pain_point_2" TEXT,
ADD COLUMN     "pain_point_3" TEXT,
ADD COLUMN     "premium_aplus_present" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "product_amazon_url" TEXT,
ADD COLUMN     "product_asin" VARCHAR(50),
ADD COLUMN     "product_slug" VARCHAR(255),
ADD COLUMN     "product_title" TEXT,
ADD COLUMN     "product_title_humanized" TEXT,
ADD COLUMN     "prospect_email" TEXT,
ADD COLUMN     "prospect_first_name" VARCHAR(255),
ADD COLUMN     "prospect_job_title" VARCHAR(255),
ADD COLUMN     "prospect_linkedin_url" TEXT,
ADD COLUMN     "prospect_revenue" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "prospect_second_name" VARCHAR(255),
ADD COLUMN     "revenue_difference_monthly" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "revenue_difference_yearly" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "revenue_source" VARCHAR(255),
ADD COLUMN     "review_category" VARCHAR(100),
ADD COLUMN     "sales_count" INTEGER,
ADD COLUMN     "search_keyword" VARCHAR(255),
ADD COLUMN     "secondary_images" JSONB DEFAULT '{}',
ADD COLUMN     "star_1_count" INTEGER,
ADD COLUMN     "star_2_count" INTEGER,
ADD COLUMN     "star_3_count" INTEGER,
ADD COLUMN     "star_4_count" INTEGER,
ADD COLUMN     "star_5_count" INTEGER,
ADD COLUMN     "storefront_present" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "storefront_url" TEXT,
ADD COLUMN     "title_char_count" INTEGER,
ADD COLUMN     "title_under_150_chars" BOOLEAN,
ADD COLUMN     "top_improvement_1" TEXT,
ADD COLUMN     "top_improvement_2" TEXT,
ADD COLUMN     "top_improvement_3" TEXT,
ADD COLUMN     "total_reviews" INTEGER,
ADD COLUMN     "video_count" INTEGER;

const express = require("express");
const multer = require("multer");
const csv = require("csv-parser");
const fs = require("fs");
const prisma = require("../database/prisma/getPrismaClient");
const { addToQueue } = require("../utils/bull/bull");

const router = express.Router();
const uploadDir = "LexReviewUploads";
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const upload = multer({ dest: uploadDir });

// GET /api/lex/reviews
router.get("/api/lex/reviews", async (req, res) => {
  try {
    const {
      seller_id,
      asin = [],
      violation,
      sort = "desc",
      rating,
      status,
      checkerStatus,
      page = 1,
      limit = 20,
    } = req.query;

    let whereClause = {};

    // Filter by seller ID
    if (seller_id) {
      const sellerIds = Array.isArray(seller_id)
        ? seller_id
        : seller_id.split(",").map((id) => id.trim());

      if (sellerIds.length > 0) {
        const sellers = await prisma.lexSeller.findMany({
          where: { sellerId: { in: sellerIds } },
          select: { id: true, sellerId: true },
        });

        if (sellers.length === 0) {
          return res.status(404).json({
            success: false,
            error: `No sellers found with IDs: ${sellerIds.join(", ")}`,
          });
        }

        const foundSellerIds = sellers.map((s) => s.sellerId);
        const notFoundSellerIds = sellerIds.filter(
          (id) => !foundSellerIds.includes(id)
        );

        if (notFoundSellerIds.length > 0) {
          console.warn(`Seller IDs not found: ${notFoundSellerIds.join(", ")}`);
        }

        whereClause.asinRef = {
          LexSellerId: { in: sellers.map((s) => s.id) },
        };
      }
    }

    // Filter by ASINs
    if (asin && asin.length > 0) {
      const asinArray = Array.isArray(asin)
        ? asin
        : asin.split(",").map((a) => a.trim());
      if (asinArray.length > 0) {
        whereClause.asin = { in: asinArray };
      }
    }

    // Filter by violation status
    if (violation !== undefined) {
      whereClause.violation = violation === "true";
    }

    // Filter by review status
    if (status) {
      const statusArray = Array.isArray(status)
        ? status
        : status.split(",").map((s) => s.trim().toUpperCase());
      const validStatuses = statusArray.filter((s) =>
        [
          "PENDING",
          "IN_PROGRESS",
          "DATA_SCRAPED",
          "AI_ANALYSIS_PENDING",
          "COMPLETED",
          "FAILED",
        ].includes(s)
      );

      if (validStatuses.length > 0) {
        if (validStatuses.length === 1) {
          whereClause.status = validStatuses[0];
        } else {
          whereClause.status = { in: validStatuses };
        }
      }
    }

    if (checkerStatus) {
      const checkerStatusArray = Array.isArray(checkerStatus)
        ? checkerStatus
        : checkerStatus.split(",").map((s) => s.trim().toUpperCase());
      const validCheckerStatuses = checkerStatusArray.filter((s) =>
        ["PENDING", "PRESENT", "REMOVED", "FAILED", "RESURRECTED"].includes(s)
      );

      if (validCheckerStatuses.length > 0) {
        if (validCheckerStatuses.length === 1) {
          whereClause.checkerStatus = validCheckerStatuses[0];
        } else {
          whereClause.checkerStatus = { in: validCheckerStatuses };
        }
      }
    }

    // Filter by rating/reviewScore - support multiple values
    if (rating) {
      const ratingArray = Array.isArray(rating)
        ? rating
        : rating.split(",").map((r) => r.trim());
      const validRatings = ratingArray
        .map((r) => parseInt(r))
        .filter((r) => r >= 1 && r <= 5);

      if (validRatings.length > 0) {
        if (validRatings.length === 1) {
          whereClause.reviewScore = validRatings[0];
        } else {
          whereClause.reviewScore = { in: validRatings };
        }
      }
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const totalCount = await prisma.lexReview.count({
      where: whereClause,
    });

    // Get reviews with pagination
    const reviews = await prisma.lexReview.findMany({
      where: whereClause,
      include: {
        asinRef: {
          select: {
            asin: true,
            title: true,
            status: true,
            sellerId: true,
            sellerName: true,
            countryCode: true,
            seller: {
              select: {
                sellerId: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: sort === "asc" ? "asc" : "desc",
      },
      skip,
      take: limitNum,
    });

    const totalPages = Math.ceil(totalCount / limitNum);

    return res.status(200).json({
      success: true,
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1,
      },
      filters: {
        seller_id: seller_id
          ? Array.isArray(seller_id)
            ? seller_id
            : seller_id.split(",")
          : null,
        asin:
          asin && asin.length > 0
            ? Array.isArray(asin)
              ? asin
              : asin.split(",")
            : null,
        violation,
        rating: rating
          ? Array.isArray(rating)
            ? rating
            : rating.split(",")
          : null,
        status: status
          ? Array.isArray(status)
            ? status
            : status.split(",")
          : null,
        sort,
        checkerStatus: checkerStatus
          ? Array.isArray(checkerStatus)
            ? checkerStatus
            : checkerStatus.split(",")
          : null,
      },
    });
  } catch (error) {
    console.error("Error in GET /api/lex/reviews:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/reviews - Create single review scraping job
router.post("/api/lex/reviews", async (req, res) => {
  try {
    const { asin, country_code = "US", sortByMostRecent = true } = req.body;

    if (!asin) {
      return res.status(400).json({
        success: false,
        error: "asin is required",
      });
    }

    if (Array.isArray(asin)) {
      return res.status(400).json({
        success: false,
        error:
          "Use /api/lex/reviews/bulk for multiple ASINs. This endpoint accepts single ASIN only.",
      });
    }

    // Check if ASIN exists
    const asinRecord = await prisma.lexASIN.findUnique({
      where: { asin, countryCode: country_code },
      select: {
        id: true,
        asin: true,
        title: true,
        status: true,
        sellerId: true,
        sellerName: true,
      },
    });

    if (!asinRecord) {
      return res.status(404).json({
        success: false,
        error: "ASIN not found. Please scrape ASIN data first.",
      });
    }

    // Check if already review pending
    if (asinRecord.status === "REVIEW_PENDING") {
      return res.status(409).json({
        success: false,
        error: "ASIN is already pending review scraping",
        asin: {
          asin: asinRecord.asin,
          title: asinRecord.title,
          status: asinRecord.status,
        },
      });
    }

    // Add job to Bull queue
    const jobData = {
      asin,
      countryCode: country_code,
      sortByMostRecent,
    };

    const bullJob = await addToQueue("singleLexReview", jobData, {
      targetQueue: "lex",
    });

    console.log(`✅ Single review job queued for ASIN: ${asin}`);

    return res.status(201).json({
      success: true,
      message: "Review scraping job queued successfully",
      job: {
        bullJobId: bullJob.id,
        asin,
        countryCode: country_code,
        sortByMostRecent,
        status: "QUEUED",
      },
      asin: {
        asin: asinRecord.asin,
        title: asinRecord.title,
        sellerId: asinRecord.sellerId,
        sellerName: asinRecord.sellerName,
      },
    });
  } catch (error) {
    console.error("Error creating single review job:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/reviews/bulk - Create bulk review scraping job
router.post(
  "/api/lex/reviews/bulk",
  upload.single("file"),
  async (req, res) => {
    try {
      let asinsToProcess = [];
      const {
        sortByMostRecent = true,
        // Filter mode parameters
        useFilters = false,
        seller_id,
        seller_name,
        reviews,
        asin,
        status,
        type,
        country_code = "US",
      } = req.body;

      if (useFilters) {
        // FILTERED MODE: Use filters to find ASINs instead of explicit list
        console.log(
          "🔍 Using filtered mode to find ASINs for bulk review scraping"
        );

        let whereClause = {};

        // Handle multiple seller IDs
        if (seller_id) {
          const sellerIds = Array.isArray(seller_id)
            ? seller_id
            : seller_id.split(",").map((id) => id.trim());

          if (sellerIds.length > 0) {
            const sellers = await prisma.lexSeller.findMany({
              where: { sellerId: { in: sellerIds } },
              select: { id: true, sellerId: true },
            });

            if (sellers.length === 0) {
              return res.status(404).json({
                success: false,
                error: `No sellers found with IDs: ${sellerIds.join(", ")}`,
              });
            }

            whereClause.LexSellerId = { in: sellers.map((s) => s.id) };
          }
        }

        // Handle multiple seller names
        if (seller_name) {
          const sellerNames = Array.isArray(seller_name)
            ? seller_name
            : seller_name.split(",").map((name) => name.trim());

          const sellerNameConditions = sellerNames.flatMap((name) => [
            {
              sellerName: {
                contains: name,
                mode: "insensitive",
              },
            },
            {
              seller: {
                name: {
                  contains: name,
                  mode: "insensitive",
                },
              },
            },
          ]);

          if (whereClause.LexSellerId) {
            whereClause.AND = [
              { LexSellerId: whereClause.LexSellerId },
              { OR: sellerNameConditions },
            ];
            delete whereClause.LexSellerId;
          } else {
            whereClause.OR = sellerNameConditions;
          }
        }

        // Handle multiple ASINs
        if (asin) {
          const asinArray = Array.isArray(asin)
            ? asin
            : asin.split(",").map((a) => a.trim());
          if (asinArray.length > 0) {
            whereClause.asin = { in: asinArray };
          }
        }

        // Handle reviews filter
        if (reviews) {
          const reviewsFilter = parseReviewsFilter(reviews);
          if (reviewsFilter) {
            whereClause.totalReviews = reviewsFilter;
          }
        }

        // Handle status filter
        if (status) {
          whereClause.status = status.toUpperCase();
        }

        // Handle asin-type filter
        if (type) {
          whereClause.type = type;
        }

        // Find ASINs matching the filters
        const filteredAsins = await prisma.lexASIN.findMany({
          where: whereClause,
          select: {
            asin: true,
            countryCode: true,
            title: true,
            status: true,
          },
          orderBy: {
            totalReviews: "desc",
          },
        });

        if (filteredAsins.length === 0) {
          return res.status(404).json({
            success: false,
            error: "No ASINs found matching the provided filters",
            appliedFilters: {
              seller_id,
              seller_name,
              reviews,
              asin,
              status,
              type,
            },
          });
        }

        asinsToProcess = filteredAsins.map((item) => ({
          asin: item.asin,
          countryCode: item.countryCode || country_code,
        }));

        console.log(`🔍 Found ${asinsToProcess.length} ASINs matching filters`);
      } else if (req.file) {
        // Handle CSV file upload
        const csvFilePath = req.file.path;

        await new Promise((resolve, reject) => {
          fs.createReadStream(csvFilePath)
            .pipe(csv())
            .on("data", (row) => {
              const asinKey = Object.keys(row).find((key) =>
                key.toLowerCase().includes("asin")
              );
              const countryKey = Object.keys(row).find(
                (key) =>
                  key.toLowerCase().includes("country") ||
                  key.toLowerCase().includes("code")
              );

              if (asinKey && row[asinKey]) {
                asinsToProcess.push({
                  asin: row[asinKey].trim(),
                  countryCode: countryKey ? row[countryKey].trim() : "US",
                });
              }
            })
            .on("end", resolve)
            .on("error", reject);
        });

        // Clean up uploaded file
        try {
          fs.unlinkSync(csvFilePath);
        } catch (cleanupError) {
          console.error("Error cleaning up uploaded file:", cleanupError);
        }
      } else if (req.body.asins) {
        // Handle array of objects or simple array
        const { asins } = req.body;

        if (!Array.isArray(asins)) {
          return res.status(400).json({
            success: false,
            error: "asins must be an array",
          });
        }

        // Support both formats:
        // 1. Array of objects: [{ asin: "B123", countryCode: "US" }, ...]
        // 2. Simple array: ["B123", "B456"] with fallback countryCode
        asinsToProcess = asins.map((item) => {
          if (typeof item === "string") {
            return {
              asin: item.trim(),
              countryCode: country_code,
            };
          } else if (typeof item === "object" && item.asin) {
            return {
              asin: item.asin.trim(),
              countryCode: item.countryCode || country_code,
            };
          } else {
            throw new Error(`Invalid ASIN format: ${JSON.stringify(item)}`);
          }
        });
      } else {
        return res.status(400).json({
          success: false,
          error: "Either CSV file, asins array, or useFilters=true is required",
        });
      }

      if (asinsToProcess.length === 0) {
        return res.status(400).json({
          success: false,
          error: "No valid ASINs found",
        });
      }

      // Remove duplicates based on asin + countryCode combination
      const uniqueAsins = asinsToProcess.filter(
        (item, index, self) =>
          index ===
          self.findIndex(
            (t) => t.asin === item.asin && t.countryCode === item.countryCode
          )
      );

      // Check which ASINs exist in database
      const existingAsins = [];
      const notFoundAsins = [];

      // Group by country for efficient database queries
      const asinsByCountry = uniqueAsins.reduce((acc, item) => {
        if (!acc[item.countryCode]) {
          acc[item.countryCode] = [];
        }
        acc[item.countryCode].push(item.asin);
        return acc;
      }, {});

      for (const [countryCode, asins] of Object.entries(asinsByCountry)) {
        const foundAsins = await prisma.lexASIN.findMany({
          where: {
            asin: { in: asins },
            countryCode: countryCode,
          },
          select: {
            id: true,
            asin: true,
            title: true,
            status: true,
            sellerId: true,
            sellerName: true,
            countryCode: true,
          },
        });

        existingAsins.push(...foundAsins);

        const foundAsinIds = foundAsins.map((a) => a.asin);
        const notFoundInCountry = asins
          .filter((asin) => !foundAsinIds.includes(asin))
          .map((asin) => ({ asin, countryCode }));

        notFoundAsins.push(...notFoundInCountry);
      }

      if (existingAsins.length === 0) {
        return res.status(404).json({
          success: false,
          error:
            "None of the ASINs exist in database. Please scrape ASIN data first.",
          notFoundAsins: notFoundAsins.slice(0, 10),
        });
      }

      // Filter out ASINs that are already review pending
      const availableAsins = existingAsins.filter(
        (asin) => asin.status !== "REVIEW_PENDING"
      );
      const alreadyPendingAsins = existingAsins.filter(
        (asin) => asin.status === "REVIEW_PENDING"
      );

      if (availableAsins.length === 0) {
        return res.status(409).json({
          success: false,
          error: "All ASINs are already pending review scraping",
          summary: {
            totalRequested: asinsToProcess.length,
            found: existingAsins.length,
            alreadyPending: alreadyPendingAsins.length,
            notFound: notFoundAsins.length,
          },
        });
      }

      // Create ONE job with all ASINs and their country codes
      const jobData = {
        asins: availableAsins.map((asin) => ({
          asin: asin.asin,
          countryCode: asin.countryCode,
        })),
        sortByMostRecent,
      };

      const bullJob = await addToQueue("bulkLexReview", jobData, {
        targetQueue: "lex",
      });

      console.log(
        `✅ Bulk review job queued for ${availableAsins.length} ASINs across ${
          Object.keys(asinsByCountry).length
        } countries`
      );

      return res.status(201).json({
        success: true,
        message: `Bulk review scraping job created for ${
          availableAsins.length
        } ASINs${useFilters ? " (using filters)" : ""}`,
        job: {
          bullJobId: bullJob.id,
          asinCount: availableAsins.length,
          countriesIncluded: [
            ...new Set(availableAsins.map((a) => a.countryCode)),
          ],
          useFilters: useFilters,
          ...(useFilters && {
            appliedFilters: {
              seller_id,
              seller_name,
              reviews,
              asin,
              status,
              type,
            },
          }),
        },
        summary: {
          totalRequested: asinsToProcess.length,
          uniqueAsins: uniqueAsins.length,
          found: existingAsins.length,
          queued: availableAsins.length,
          alreadyPending: alreadyPendingAsins.length,
          notFound: notFoundAsins.length,
          countriesProcessed: [
            ...new Set(availableAsins.map((a) => a.countryCode)),
          ],
        },
        queuedAsins: availableAsins.slice(0, 10).map((a) => ({
          asin: a.asin,
          countryCode: a.countryCode,
          title:
            a.title?.substring(0, 50) + (a.title?.length > 50 ? "..." : ""),
        })),
        ...(notFoundAsins.length > 0 && {
          notFoundAsins: notFoundAsins
            .slice(0, 10)
            .map((item) => `${item.asin} (${item.countryCode})`),
        }),
        ...(alreadyPendingAsins.length > 0 && {
          alreadyPendingAsins: alreadyPendingAsins
            .slice(0, 10)
            .map((a) => `${a.asin} (${a.countryCode})`),
        }),
      });
    } catch (error) {
      console.error("Error creating bulk review job:", error);

      // Clean up file if error occurs
      if (req.file && req.file.path) {
        try {
          if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
        } catch (cleanupError) {
          console.error("Error cleaning up file:", cleanupError);
        }
      }

      return res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  }
);

// GET /api/lex/reviews/stats - Get review statistics
router.get("/api/lex/reviews/stats", async (req, res) => {
  try {
    // Get overall review stats
    const totalReviews = await prisma.lexReview.count();
    const violationReviews = await prisma.lexReview.count({
      where: { violation: true },
    });

    // Get rating distribution
    const ratingStats = await prisma.lexReview.groupBy({
      by: ["reviewScore"],
      _count: { id: true },
      orderBy: { reviewScore: "asc" },
    });

    // Get recent reviews
    const recentReviews = await prisma.lexReview.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      include: {
        asinRef: {
          select: {
            asin: true,
            title: true,
          },
        },
      },
    });

    // Get review jobs stats
    const reviewJobStats = await prisma.lexJob.groupBy({
      by: ["type", "status"],
      where: {
        type: { in: ["SINGLE_REVIEW", "BULK_REVIEW"] },
      },
      _count: { id: true },
    });

    return res.status(200).json({
      success: true,
      stats: {
        totalReviews,
        violationReviews,
        violationPercentage:
          totalReviews > 0
            ? ((violationReviews / totalReviews) * 100).toFixed(2)
            : 0,
        ratingDistribution: ratingStats.reduce((acc, stat) => {
          acc[`${stat.reviewScore}_star`] = stat._count.id;
          return acc;
        }, {}),
        jobStats: reviewJobStats.reduce((acc, stat) => {
          if (!acc[stat.type]) acc[stat.type] = {};
          acc[stat.type][stat.status] = stat._count.id;
          return acc;
        }, {}),
      },
      recentReviews,
    });
  } catch (error) {
    console.error("Error in GET /api/lex/reviews/stats:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/reviews/download - Updated with multiple filters support
router.post("/api/lex/reviews/download", async (req, res) => {
  try {
    const {
      asins = [],
      seller_id,
      seller_name,
      violation,
      rating,
      status,
      dateFrom,
      dateTo,
      metaData = false, // New parameter
    } = req.body;

    let whereClause = {};

    // Handle multiple seller IDs
    if (seller_id) {
      const sellerIds = Array.isArray(seller_id)
        ? seller_id
        : seller_id.split(",").map((id) => id.trim());

      if (sellerIds.length > 0) {
        const sellers = await prisma.lexSeller.findMany({
          where: { sellerId: { in: sellerIds } },
          select: { id: true, sellerId: true },
        });

        if (sellers.length === 0) {
          return res.status(404).json({
            success: false,
            error: `No sellers found with IDs: ${sellerIds.join(", ")}`,
          });
        }

        const foundSellerIds = sellers.map((s) => s.sellerId);
        const notFoundSellerIds = sellerIds.filter(
          (id) => !foundSellerIds.includes(id)
        );

        if (notFoundSellerIds.length > 0) {
          console.warn(`Seller IDs not found: ${notFoundSellerIds.join(", ")}`);
        }

        whereClause.asinRef = {
          LexSellerId: { in: sellers.map((s) => s.id) },
        };
      }
    }

    // Handle multiple seller names
    if (seller_name) {
      const sellerNames = Array.isArray(seller_name)
        ? seller_name
        : seller_name.split(",").map((name) => name.trim());

      const sellerNameConditions = sellerNames.flatMap((name) => [
        {
          asinRef: {
            sellerName: {
              contains: name,
              mode: "insensitive",
            },
          },
        },
        {
          asinRef: {
            seller: {
              name: {
                contains: name,
                mode: "insensitive",
              },
            },
          },
        },
      ]);

      if (whereClause.asinRef) {
        // If we already have seller ID filter, combine with AND
        whereClause.AND = [
          { asinRef: whereClause.asinRef },
          { OR: sellerNameConditions },
        ];
        delete whereClause.asinRef;
      } else {
        // If no seller ID filter, just use seller name conditions
        whereClause.OR = sellerNameConditions;
      }
    }

    // Handle multiple ASINs
    if (asins && asins.length > 0) {
      const asinArray = Array.isArray(asins)
        ? asins
        : asins.split(",").map((a) => a.trim());
      if (asinArray.length > 0) {
        whereClause.asin = { in: asinArray };
      }
    }

    // Filter by violation status
    if (violation !== undefined) {
      if (typeof violation === "string") {
        whereClause.violation = violation === "true";
      } else {
        whereClause.violation = violation;
      }
    }

    // Filter by review status
    if (status) {
      const statusArray = Array.isArray(status)
        ? status
        : status.split(",").map((s) => s.trim().toUpperCase());
      const validStatuses = statusArray.filter((s) =>
        [
          "PENDING",
          "IN_PROGRESS",
          "DATA_SCRAPED",
          "AI_ANALYSIS_PENDING",
          "COMPLETED",
          "FAILED",
        ].includes(s)
      );

      if (validStatuses.length > 0) {
        if (validStatuses.length === 1) {
          whereClause.status = validStatuses[0];
        } else {
          whereClause.status = { in: validStatuses };
        }
      }
    }

    // Filter by rating
    if (rating) {
      const ratingArray = Array.isArray(rating)
        ? rating
        : rating.split(",").map((r) => r.trim());
      const validRatings = ratingArray
        .map((r) => parseInt(r))
        .filter((r) => !isNaN(r) && r >= 1 && r <= 5);

      if (validRatings.length > 0) {
        if (validRatings.length === 1) {
          whereClause.reviewScore = validRatings[0];
        } else {
          whereClause.reviewScore = { in: validRatings };
        }
      }
    }

    // Filter by date range
    if (dateFrom || dateTo) {
      whereClause.createdAt = {};
      if (dateFrom) {
        try {
          whereClause.createdAt.gte = new Date(dateFrom);
        } catch (e) {
          console.warn("Invalid dateFrom format:", dateFrom);
        }
      }
      if (dateTo) {
        try {
          const endDate = new Date(dateTo);
          endDate.setHours(23, 59, 59, 999);
          whereClause.createdAt.lte = endDate;
        } catch (e) {
          console.warn("Invalid dateTo format:", dateTo);
        }
      }
    }

    console.log(`Downloading reviews with filters:`, {
      seller_id,
      seller_name,
      asins: asins && asins.length > 0 ? asins : null,
      violation,
      rating,
      status,
      dateFrom,
      dateTo,
      whereClause: JSON.stringify(whereClause, null, 2),
    });

    // Filter for specific countries
    whereClause.reviewerCountry = {
      in: ["United States", "United Kingdom", "Canada"],
    };

    const reviewData = await prisma.lexReview.findMany({
      where: whereClause,
      include: {
        asinRef: {
          select: {
            asin: true,
            title: true,
            status: true,
            sellerId: true,
            sellerName: true,
            countryCode: true,
            seller: {
              select: {
                sellerId: true,
                name: true,
                status: true,
              },
            },
          },
        },
        // Include metadata if requested
        metadata: metaData === true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (reviewData.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No reviews found matching the criteria",
        appliedFilters: {
          seller_id: seller_id
            ? Array.isArray(seller_id)
              ? seller_id
              : seller_id.split(",")
            : null,
          seller_name: seller_name
            ? Array.isArray(seller_name)
              ? seller_name
              : seller_name.split(",")
            : null,
          asins: asins && asins.length > 0 ? asins : null,
          violation,
          rating: rating
            ? Array.isArray(rating)
              ? rating
              : rating.split(",")
            : null,
          status: status
            ? Array.isArray(status)
              ? status
              : status.split(",")
            : null,
          dateFrom,
          dateTo,
        },
      });
    }

    console.log(`✅ Found ${reviewData.length} reviews`);

    let dynamicMetaDataHeaders = [];
    if (metaData && reviewData.length > 0 && reviewData[0].metadata) {
      const flattenedExampleMetadata = flattenMetadata(reviewData[0].metadata);
      dynamicMetaDataHeaders = Object.keys(flattenedExampleMetadata).map(
        (key) => `Metadata: ${key}`
      );
    }

    // CSV headers
    const csvHeaders = [
      "Review ID",
      "ASIN",
      "ProductTitle",
      "ProductLink",
      "Seller ID",
      "Seller Name",
      "Reviewer",
      "ReviewerCountry",
      "ReviewerLink",
      "ReviewScore",
      "Review Title",
      "ReviewContent",
      "ReviewDate",
      "HelpfulCounts",
      "isVerified",
      "Page URL",
      "Violation",
      "Prompt1 Output",
      "Prompt2 Output",
      "GuidelineViolation1",
      "ConfidenceScore1",
      "Reason1",
      "Prompt3 Output",
      "Review URL",
      "Images",
      "Variants",
      "Review Screenshot",
      "Created At",
      "ASIN CountryCode",
      "ReviewStatus",
      ...dynamicMetaDataHeaders,
    ];

    const csvRows = reviewData.map((review) => {
      // Parse Prompt2 Output to extract the new columns
      const parsedPrompt2 = parsePrompt2Output(review.prompt2Output);

      let row = [
        review.reviewID || "",
        review.asin || "",
        `"${(review.asinRef?.title || "").replace(/"/g, '""')}"`,
        review.productLink || "",
        review.asinRef?.sellerId || "",
        `"${(review.asinRef?.sellerName || "").replace(/"/g, '""')}"`,
        `"${(review.reviewer || "").replace(/"/g, '""')}"`,
        review.reviewerCountry || "",
        review.reviewerLink || "",
        review.reviewScore || "",
        `"${(review.reviewTitle || "").replace(/"/g, '""')}"`,
        `"${(review.reviewContent || "").replace(/"/g, '""')}"`,
        formatDateToDDMMYYYY(review.reviewDate),
        review.HelpfulCounts || 0,
        review.isVerified || false,
        review.pageUrl || "",
        review.violation || false,
        `"${(review.prompt1Output || "").replace(/"/g, '""')}"`,
        `"${(review.prompt2Output || "").replace(/"/g, '""')}"`,
        `"${(parsedPrompt2.GuidelineViolation1 || "").replace(/"/g, '""')}"`,
        parsedPrompt2.ConfidenceScore1 || 0,
        `"${(parsedPrompt2.Reason1 || "").replace(/"/g, '""')}"`,
        `"${(review.prompt3Output || "")
          .replace(/\n/g, " ")
          .replace(/"/g, '""')}"`,
        review.reviewLink || "",
        [review.image1, review.image2, review.image3, review.image4]
          .filter((img) => img)
          .join("; "),
        [review.variant_0, review.variant_1].filter((v) => v).join("; "),
        review.reviewScreenshot || "", // Add screenshot URL
        review.createdAt ? review.createdAt.toISOString() : "",
        review.asinRef?.countryCode || "",
        review.status || "",
      ];

      if (metaData && review.metadata) {
        const flattened = flattenMetadata(review.metadata);
        dynamicMetaDataHeaders.forEach((header) => {
          const key = header.replace("Metadata: ", "");
          let value = flattened[key];
          if (typeof value === "object" && value !== null) {
            value = JSON.stringify(value); // Stringify objects/arrays within metadata
          }
          row.push(`"${(value || "").toString().replace(/"/g, '""')}"`);
        });
      }
      return row;
    });

    const csvContent = [
      csvHeaders.join(","),
      ...csvRows.map((row) => row.join(",")),
    ].join("\n");

    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .slice(0, -5);
    const filename = `reviews_data_${timestamp}--${reviewData.length}-items.csv`;

    console.log(`Generating CSV file: ${filename}`);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Content-Length", Buffer.byteLength(csvContent));

    return res.status(200).send(csvContent);
  } catch (error) {
    console.error("Error in POST /api/lex/reviews/download:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

// POST /api/lex/reviews/download/reviewIds - Download specific reviews by Review IDs as CSV
router.post("/api/lex/reviews/download/reviewIds", async (req, res) => {
  try {
    const { reviewIds = [], metaData = false } = req.body;

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "reviewIds array is required and cannot be empty",
      });
    }

    console.log(`Downloading ${reviewIds.length} specific reviews by IDs...`);

    const reviewData = await prisma.lexReview.findMany({
      where: {
        reviewID: { in: reviewIds },
      },
      include: {
        asinRef: {
          select: {
            asin: true,
            title: true,
            seller: {
              select: {
                sellerId: true,
                name: true,
              },
            },
          },
        },
        metadata: metaData === true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (reviewData.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No reviews found matching the provided Review IDs",
      });
    }

    // Check which review IDs were not found
    const foundReviewIds = reviewData.map((review) => review.reviewID);
    const notFoundReviewIds = reviewIds.filter(
      (id) => !foundReviewIds.includes(id)
    );

    if (notFoundReviewIds.length > 0) {
      console.log(`⚠️ Review IDs not found: ${notFoundReviewIds.join(", ")}`);
    }

    console.log(
      `✅ Found ${reviewData.length} out of ${reviewIds.length} requested reviews`
    );

    const csvHeaders = [
      "Review ID",
      "ASIN",
      "ProductTitle",
      "ProductLink",
      "Seller ID",
      "Seller Name",
      "Reviewer",
      "ReviewerCountry",
      "ReviewerLink",
      "ReviewScore",
      "Review Title",
      "ReviewContent",
      "ReviewDate",
      "HelpfulCounts",
      "isVerified",
      "Page URL",
      "Violation",
      "Prompt1 Output",
      "Prompt2 Output",
      "GuidelineViolation1",
      "ConfidenceScore1",
      "Reason1",
      "Prompt3 Output",
      "Review URL",
      "Images",
      "Variants",
      "Created At",
    ];

    const csvRows = reviewData.map((review) => {
      // Parse Prompt2 Output to extract the new columns
      const parsedPrompt2 = parsePrompt2Output(review.prompt2Output);

      return [
        review.reviewID || "",
        review.asin || "",
        `"${(review.asinRef?.title || "").replace(/"/g, '""')}"`,
        review.productLink || "",
        review.asinRef?.seller?.sellerId || "",
        `"${(review.asinRef?.seller?.name || "").replace(/"/g, '""')}"`,
        `"${(review.reviewer || "").replace(/"/g, '""')}"`,
        review.reviewerCountry || "",
        review.reviewerLink || "",
        review.reviewScore || "",
        `"${(review.reviewTitle || "").replace(/"/g, '""')}"`,
        `"${(review.reviewContent || "").replace(/"/g, '""')}"`,
        formatDateToDDMMYYYY(review.reviewDate),
        review.HelpfulCounts || 0,
        review.isVerified || "False",
        review.pageUrl || "",
        review.violation || false,
        `"${(review.prompt1Output || "").replace(/"/g, '""')}"`,
        `"${(review.prompt2Output || "").replace(/"/g, '""')}"`,
        `"${(parsedPrompt2.GuidelineViolation1 || "").replace(/"/g, '""')}"`,
        parsedPrompt2.ConfidenceScore1 || 0,
        `"${(parsedPrompt2.Reason1 || "").replace(/"/g, '""')}"`,
        `"${(review.prompt3Output || "")
          .replace(/\n/g, " ")
          .replace(/"/g, '""')}"`,
        review.reviewLink || "",
        [review.image1, review.image2, review.image3, review.image4]
          .filter((img) => img)
          .join("; "),
        [review.variant_0, review.variant_1].filter((v) => v).join("; "),
        review.createdAt ? review.createdAt.toISOString() : "",
      ];
    });

    const csvContent = [
      csvHeaders.join(","),
      ...csvRows.map((row) => row.join(",")),
    ].join("\n");

    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .slice(0, -5);
    const filename = `reviews_by_ids_${timestamp}_${reviewData.length}_items.csv`;

    console.log(`Generating CSV file: ${filename}`);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Content-Length", Buffer.byteLength(csvContent));

    return res.status(200).send(csvContent);
  } catch (error) {
    console.error("Error in POST /api/lex/reviews/download/by-ids:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

function flattenMetadata(metadata, prefix = "") {
  let flattened = {};
  for (const key in metadata) {
    if (metadata.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (
        typeof metadata[key] === "object" &&
        metadata[key] !== null &&
        !Array.isArray(metadata[key])
      ) {
        Object.assign(flattened, flattenMetadata(metadata[key], newKey));
      } else {
        flattened[newKey] = metadata[key];
      }
    }
  }
  return flattened;
}

function parsePrompt2Output(prompt2Output) {
  try {
    if (!prompt2Output)
      return { GuidelineViolation1: "", ConfidenceScore1: 0, Reason1: "" };

    const parsed = JSON.parse(prompt2Output);
    return {
      GuidelineViolation1: parsed.GuidelineViolation1 || "",
      ConfidenceScore1: parsed.ConfidenceScore1 || 0,
      Reason1: parsed.Reason1 || "",
    };
  } catch (error) {
    console.error("Error parsing Prompt2 Output:", error);
    return { GuidelineViolation1: "", ConfidenceScore1: 0, Reason1: "" };
  }
}

function formatDateToDDMMYYYY(date) {
  if (!date) return "";

  try {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
}

// HELPER FUNCTIONS
function parseReviewsFilter(reviewsParam) {
  const match = reviewsParam.match(/^(gt|lt|gte|lte|eq)(\d+)$/);
  if (!match) return null;

  const [, operator, value] = match;
  const numValue = parseInt(value);

  switch (operator) {
    case "gt":
      return { gt: numValue };
    case "gte":
      return { gte: numValue };
    case "lt":
      return { lt: numValue };
    case "lte":
      return { lte: numValue };
    case "eq":
      return { equals: numValue };
    default:
      return null;
  }
}

module.exports = router;

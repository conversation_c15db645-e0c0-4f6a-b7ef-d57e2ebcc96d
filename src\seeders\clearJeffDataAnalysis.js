#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to clear all data from JeffDataAnalysis table
 * Usage: node src/seeders/clearJeffDataAnalysis.js
 */

const prisma = require("../database/prisma/getPrismaClient");

async function clearJeffDataAnalysis() {
    try {
        console.log("🧹 Starting to clear JeffDataAnalysis table...");

        // First check if the table exists
        try {
            const count = await prisma.jeffDataAnalysis.count();
            console.log(`📊 Found ${count} records in JeffDataAnalysis table`);

            if (count === 0) {
                console.log("✅ Table is already empty!");
                return;
            }
        } catch (error) {
            console.error("❌ JeffDataAnalysis table does not exist or cannot be accessed:");
            console.error("   ", error.message);
            return;
        }

        // Delete all records
        console.log("🗑️  Deleting all records...");
        const result = await prisma.jeffDataAnalysis.deleteMany({});

        console.log(`✅ Successfully deleted ${result.count} records from JeffDataAnalysis table`);

        // Reset the auto-increment index to 0
        console.log("🔄 Resetting auto-increment index...");
        try {
            await prisma.$executeRaw`ALTER SEQUENCE "JeffDataAnalysis_id_seq" RESTART WITH 1`;
            console.log("✅ Auto-increment index reset to 1");
        } catch (resetError) {
            console.warn("⚠️  Could not reset auto-increment index (this is normal for some databases):", resetError.message);
            console.log("ℹ️  The next record will continue from the previous sequence");
        }

    } catch (error) {
        console.error("❌ Error clearing JeffDataAnalysis table:", error);
        throw error;
    }
}

async function main() {
    try {
        await clearJeffDataAnalysis();
        console.log("🎉 JeffDataAnalysis table cleared successfully!");
    } catch (error) {
        console.error("❌ Failed to clear JeffDataAnalysis table:", error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
        process.exit(0);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    clearJeffDataAnalysis
};

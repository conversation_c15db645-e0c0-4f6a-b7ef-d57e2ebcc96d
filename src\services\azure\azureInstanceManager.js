// Load environment variables first
require('dotenv').config();

const { DefaultAzureCredential } = require("@azure/identity");
const { ComputeManagementClient } = require("@azure/arm-compute");
const { NetworkManagementClient } = require("@azure/arm-network");

// Check if Azure credentials are configured
const subscriptionId = process.env.AZURE_SUBSCRIPTION_ID;
const resourceGroupName = process.env.RESOURCE_GROUP_NAME;
const location = process.env.AZURE_LOCATION || 'Central India';

if (!subscriptionId || !resourceGroupName) {
  console.warn('Azure credentials are not properly configured in environment variables.');
  console.warn('Please ensure AZURE_SUBSCRIPTION_ID and RESOURCE_GROUP_NAME are set in the .env file.');
}

// Configure Azure SDK with DefaultAzureCredential
const credential = new DefaultAzureCredential();
const computeClient = new ComputeManagementClient(credential, subscriptionId);
const networkClient = new NetworkManagementClient(credential, subscriptionId);

// Check if the user has Azure permissions
async function validateAzurePermissions() {
  try {
    // Attempt a simple Azure operation to check permissions
    await computeClient.virtualMachines.list(resourceGroupName);
    return {
      hasPermission: true
    };
  } catch (error) {
    console.error('Azure permission validation error:', error);

    // Determine the type of error
    let errorDetails = {
      hasPermission: false,
      message: error.message,
      recommendation: 'Please check your Azure credentials and permissions.'
    };

    if (error.message.includes('credentials')) {
      errorDetails.recommendation = 'Azure credentials are missing or invalid. Please configure AZURE_TENANT_ID, AZURE_CLIENT_ID, and AZURE_CLIENT_SECRET in your .env file.';
    } else if (error.message.includes('subscription')) {
      errorDetails.recommendation = 'Azure subscription ID is missing or invalid. Please check AZURE_SUBSCRIPTION_ID in your .env file.';
    } else if (error.message.includes('authorization')) {
      errorDetails.recommendation = 'Insufficient permissions to access Azure resources. Please ensure your service principal has the required permissions.';
    }

    return errorDetails;
  }
}

// List all VMs in the resource group
async function listInstances() {
  try {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Fetching Azure VM instances...`);

    const vms = [];
    for await (const vm of computeClient.virtualMachines.list(resourceGroupName)) {
      // Get instance view for power state
      const instanceView = await computeClient.virtualMachines.instanceView(
        resourceGroupName, 
        vm.name
      );
      
      // Extract power state
      const powerState = instanceView.statuses?.find(status => 
        status.code?.startsWith('PowerState/')
      )?.displayStatus || 'Unknown';

      // Get public IP if available
      let publicIpAddress = null;
      if (vm.networkProfile?.networkInterfaces?.length > 0) {
        try {
          const nicId = vm.networkProfile.networkInterfaces[0].id;
          const nicName = nicId.split('/').pop();
          const nic = await networkClient.networkInterfaces.get(resourceGroupName, nicName);
          
          if (nic.ipConfigurations?.[0]?.publicIPAddress) {
            const publicIpId = nic.ipConfigurations[0].publicIPAddress.id;
            const publicIpName = publicIpId.split('/').pop();
            const publicIp = await networkClient.publicIPAddresses.get(resourceGroupName, publicIpName);
            publicIpAddress = publicIp.ipAddress;
          }
        } catch (ipError) {
          console.warn(`Could not fetch public IP for VM ${vm.name}:`, ipError.message);
        }
      }

      vms.push({
        InstanceId: vm.name,
        Name: vm.name,
        State: mapPowerStateToAWSEquivalent(powerState),
        InstanceType: vm.hardwareProfile?.vmSize || 'Unknown',
        LaunchTime: vm.timeCreated || null,
        PublicIpAddress: publicIpAddress,
        PrivateIpAddress: vm.networkProfile?.networkInterfaces?.[0]?.primary ? 'Primary' : null,
        Location: vm.location,
        ResourceGroup: resourceGroupName,
        PowerState: powerState
      });
    }

    console.log(`[${timestamp}] Found ${vms.length} Azure VM instances`);
    return vms;
  } catch (error) {
    console.error('Error listing Azure VM instances:', error);
    throw new Error(`Failed to list Azure VM instances: ${error.message}`);
  }
}

// Map Azure power states to AWS equivalent states for compatibility
function mapPowerStateToAWSEquivalent(powerState) {
  const stateMap = {
    'VM running': 'running',
    'VM stopped': 'stopped',
    'VM deallocated': 'stopped',
    'VM starting': 'pending',
    'VM stopping': 'stopping',
    'VM deallocating': 'stopping'
  };
  return stateMap[powerState] || 'unknown';
}

// Start a VM instance
async function startInstance(vmName) {
  try {
    // Validate Azure permissions before proceeding
    const permissionCheck = await validateAzurePermissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to start VM: ${permissionCheck.recommendation}`
      };
    }

    // Check VM current state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === vmName);

    if (!instance) {
      return {
        success: false,
        message: `VM ${vmName} not found in resource group ${resourceGroupName}`
      };
    }

    // If the VM is already running, return success
    if (instance.State === 'running') {
      return {
        success: true,
        message: `VM ${vmName} is already running`
      };
    }

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Starting Azure VM: ${vmName}`);

    const poller = await computeClient.virtualMachines.beginStart(
      resourceGroupName,
      vmName
    );
    await poller.pollUntilDone();

    console.log(`[${timestamp}] VM ${vmName} started successfully`);

    return {
      success: true,
      message: `VM ${vmName} started successfully`
    };
  } catch (error) {
    console.error(`Error starting VM ${vmName}:`, error);
    return {
      success: false,
      message: `Failed to start VM ${vmName}: ${error.message}`
    };
  }
}

// Stop a VM instance
async function stopInstance(vmName) {
  try {
    // Validate Azure permissions before proceeding
    const permissionCheck = await validateAzurePermissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to stop VM: ${permissionCheck.recommendation}`
      };
    }

    // Check VM current state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === vmName);

    if (!instance) {
      return {
        success: false,
        message: `VM ${vmName} not found in resource group ${resourceGroupName}`
      };
    }

    // If the VM is already stopped, return success
    if (instance.State === 'stopped') {
      return {
        success: true,
        message: `VM ${vmName} is already stopped`
      };
    }

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Stopping Azure VM: ${vmName}`);

    // Use deallocate to fully stop the VM and release compute resources
    const poller = await computeClient.virtualMachines.beginDeallocate(
      resourceGroupName,
      vmName
    );
    await poller.pollUntilDone();

    console.log(`[${timestamp}] VM ${vmName} stopped successfully`);

    return {
      success: true,
      message: `VM ${vmName} stopped successfully`
    };
  } catch (error) {
    console.error(`Error stopping VM ${vmName}:`, error);
    return {
      success: false,
      message: `Failed to stop VM ${vmName}: ${error.message}`
    };
  }
}

// Reboot a VM instance
async function rebootInstance(vmName) {
  try {
    // Validate Azure permissions before proceeding
    const permissionCheck = await validateAzurePermissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to reboot VM: ${permissionCheck.recommendation}`
      };
    }

    // Check VM current state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === vmName);

    if (!instance) {
      return {
        success: false,
        message: `VM ${vmName} not found in resource group ${resourceGroupName}`
      };
    }

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Rebooting Azure VM: ${vmName}`);

    const poller = await computeClient.virtualMachines.beginRestart(
      resourceGroupName,
      vmName
    );
    await poller.pollUntilDone();

    console.log(`[${timestamp}] VM ${vmName} rebooted successfully`);

    return {
      success: true,
      message: `VM ${vmName} rebooted successfully`
    };
  } catch (error) {
    console.error(`Error rebooting VM ${vmName}:`, error);
    return {
      success: false,
      message: `Failed to reboot VM ${vmName}: ${error.message}`
    };
  }
}

// Run startup script on a VM instance (equivalent to AWS SSM)
async function runStartupScript(vmName) {
  try {
    // Validate Azure permissions before proceeding
    const permissionCheck = await validateAzurePermissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to run startup script: ${permissionCheck.recommendation}`
      };
    }

    // Check VM current state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === vmName);

    if (!instance) {
      return {
        success: false,
        message: `VM ${vmName} not found in resource group ${resourceGroupName}`
      };
    }

    if (instance.State !== 'running') {
      return {
        success: false,
        message: `VM ${vmName} must be running to execute startup script. Current state: ${instance.State}`
      };
    }

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Running startup script on Azure VM: ${vmName}`);

    // Note: Azure VM Run Command equivalent to AWS SSM
    // This would require the Azure VM Run Command extension
    // For now, we'll return a success message indicating the script would be run
    // In a full implementation, you would use:
    // const { ComputeManagementClient } = require("@azure/arm-compute");
    // await computeClient.virtualMachineRunCommands.beginCreateOrUpdate(...)

    console.log(`[${timestamp}] Startup script execution initiated on VM ${vmName}`);

    return {
      success: true,
      message: `Startup script execution initiated on VM ${vmName}`,
      note: 'Azure VM Run Command would be used for actual script execution'
    };
  } catch (error) {
    console.error(`Error running startup script on VM ${vmName}:`, error);
    return {
      success: false,
      message: `Failed to run startup script on VM ${vmName}: ${error.message}`
    };
  }
}

// Example usage for testing (commented out)
async function exampleUsage() {
  const testVmName = 'test-vm'; // Replace with actual VM name

  // Test listing instances
  console.log('Testing listInstances...');
  listInstances().then(instances => {
    console.log('Instances:', instances);
  }).catch(console.error);

  // Test starting an instance
  // startInstance(testVmName).then(result => {
  //   console.log('Start instance result:', result);
  // }).catch(console.error);

  // Test stopping an instance
  // stopInstance(testVmName).then(result => {
  //   console.log('Stop instance result:', result);
  // }).catch(console.error);

  // Test rebooting an instance
  // rebootInstance(testVmName).then(result => {
  //   console.log('Reboot instance result:', result);
  // }).catch(console.error);

  // Test running startup script
  // runStartupScript(testVmName).then(result => {
  //   console.log('Startup script result:', result);
  // }).catch(console.error);
}

module.exports = {
  validateAzurePermissions,
  listInstances,
  startInstance,
  stopInstance,
  rebootInstance,
  runStartupScript
};

const axios = require("axios");
const { executeLocally } = require("./imageProcessor");
require("dotenv").config();

// Azure Function configuration
const AZURE_FUNCTION_URL = process.env.AZURE_FUNCTION_URL; // URL of deployed Azure Function
const AZURE_FUNCTION_KEY = process.env.AZURE_FUNCTION_KEY; // Function key for authentication

async function triggerAzureFunction(action, url, identifier) {
  // If Azure Function URL is configured, call the deployed function
  if (AZURE_FUNCTION_URL) {
    return await callDeployedAzureFunction(action, url, identifier);
  } else {
    // Fallback to local execution
    console.log("Azure Function URL not configured, executing locally...");
    return await executeLocalFunction(action, url, identifier);
  }
}

async function callDeployedAzureFunction(action, url, identifier) {
  const functionUrl = `${AZURE_FUNCTION_URL}/api/imageProcessor`;
  
  const payload = {
    action: action,
    url: url,
    identifier: identifier,
  };

  const headers = {
    'Content-Type': 'application/json'
  };

  // Add function key if available
  if (AZURE_FUNCTION_KEY) {
    headers['x-functions-key'] = AZURE_FUNCTION_KEY;
  }

  try {
    console.log(`Calling Azure Function: ${action} for ${identifier}`);
    
    const response = await axios.post(functionUrl, payload, {
      headers: headers,
      timeout: 300000 // 5 minutes timeout for image/PDF generation
    });

    console.log("Azure Function response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error invoking Azure Function:", error.message);
    
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
    
    throw error;
  }
}

async function executeLocalFunction(action, url, identifier) {
  try {
    console.log(`Executing Azure Function locally: ${action} for ${identifier}`);
    
    const result = await executeLocally(action, url, identifier);
    
    console.log("Local execution result:", result);
    return result;
  } catch (error) {
    console.error("Error executing function locally:", error);
    throw error;
  }
}

// Health check function
async function checkAzureFunctionHealth() {
  if (!AZURE_FUNCTION_URL) {
    return {
      status: 'local',
      message: 'Azure Function URL not configured, using local execution'
    };
  }

  try {
    const healthUrl = `${AZURE_FUNCTION_URL}/api/health`;
    const headers = {};
    
    if (AZURE_FUNCTION_KEY) {
      headers['x-functions-key'] = AZURE_FUNCTION_KEY;
    }

    const response = await axios.get(healthUrl, {
      headers: headers,
      timeout: 10000 // 10 seconds timeout for health check
    });

    return {
      status: 'healthy',
      message: 'Azure Function is responding',
      data: response.data
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `Azure Function health check failed: ${error.message}`,
      fallback: 'Will use local execution'
    };
  }
}

// Wrapper function to maintain compatibility with existing Lambda trigger calls
async function triggerFunction(action, url, identifier) {
  return await triggerAzureFunction(action, url, identifier);
}

module.exports = {
  triggerAzureFunction,
  triggerFunction, // For backward compatibility
  checkAzureFunctionHealth,
  executeLocalFunction
};

// Example usage:
// triggerAzureFunction("generateImages", "https://www.amazon.com/Calendar-Disc-Bound-Planners-Levenger-Notebook/dp/B06XY4VDXR/ref=sr_1_1?m=AMKIKL8YPWG08&marketplaceID=ATVPDKIKX0DER&s=merchant-items&sr=1-1B06XY4VDXR", "BOSCH1");
// triggerAzureFunction("generatePDF", "https://www.amazon.com/Calendar-Disc-Bound-Planners-Levenger-Notebook/dp/B06XY4VDXR/ref=sr_1_1?m=AMKIKL8YPWG08&marketplaceID=ATVPDKIKX0DER&s=merchant-items&sr=1-1B06XY4VDXR", "BOSCH1");

const { completionFactory } = require("../../services/scrapeGPT/factory");
const { parseYamlReport } = require("../../utils/yamlParser");
const titleLengthGuidelines = {
  "Automotive & Powersports (Parts & Accessories)": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Automotive & Powersports (Tires & Wheels)": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  Baby: {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  Beauty: {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Clothing & Accessories": {
    bad: [0, 50],
    average: [50, 70],
    good: [70, Infinity],
    max: 80,
  },
  "Consumer Electronics": {
    bad: [0, 110],
    average: [110, 130],
    good: [130, Infinity],
    max: 150,
  },
  "Entertainment Collectibles": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Eyewear (See Clothing & Accessories)": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Fine Art": {
    bad: [0, 150],
    average: [150, 180],
    good: [180, Infinity],
    max: 200,
  },
  "Gift Cards": {
    bad: [0, 50],
    average: [50, 70],
    good: [70, Infinity],
    max: 80,
  },
  "Grocery & Gourmet Food": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Health & Personal Care": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Home, Home Decor, Kitchen & Garden Furniture": {
    bad: [0, 150],
    average: [150, 180],
    good: [180, Infinity],
    max: 200,
  },
  "Industrial & Scientific": {
    bad: [0, 110],
    average: [110, 130],
    good: [130, Infinity],
    max: 150,
  },
  Jewelry: {
    bad: [0, 45],
    average: [45, 65],
    good: [65, Infinity],
    max: 100,
  },
  Lighting: {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Luggage & Travel Accessories": {
    bad: [0, 50],
    average: [50, 70],
    good: [70, Infinity],
    max: 80,
  },
  "Motorcycles & ATVs": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Musical Instruments": {
    bad: [0, 110],
    average: [110, 130],
    good: [130, Infinity],
    max: 150,
  },
  "Office Products": {
    bad: [0, 110],
    average: [110, 130],
    good: [130, Infinity],
    max: 150,
  },
  Outdoors: {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Pet Supplies": {
    bad: [0, 150],
    average: [150, 180],
    good: [180, Infinity],
    max: 200,
  },
  "Shoes, Handbags & Sunglasses": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  Sports: {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Tools & Home Improvement": {
    bad: [0, 35],
    average: [35, 45],
    good: [45, Infinity],
    max: 50,
  },
  "Toys & Games": {
    bad: [0, 150],
    average: [150, 180],
    good: [180, Infinity],
    max: 200,
  },
  Watches: {
    bad: [0, 5],
    average: [5, 10],
    good: [10, Infinity],
    max: 10,
  }, Default: {
    bad: [0, 150],
    average: [150, 180],
    good: [180, Infinity],
    max: [Infinity],
  },
};




async function getProductTitleReport(data, report, clientId, ymlPath) {
  if (!data.productData?.[0]?.productTitle) {
    return;
  }

  const titleLength = data.productData[0].productTitle.numOfChars;
  let productCategory = data.productData[0].categoryAndRank || "N/A";
  // Adding selected caseStudies
  if (Array.isArray(productCategory) && productCategory.length > 0) {
    const element = productCategory[0];
    if (element && element.category) {
      productCategory = element.category;
    } else {
      productCategory = element;
    }
  } else {
    productCategory = "N/A";
  }

  if (productCategory === "N/A") {
    productCategory = data.productData[0].productTitle;
  }
  const options = {
    functionName: "getProductTitleReport",
    customTags: [
      `ClientId:${clientId}`,
      `BSRCategoryMatch`,
      `AmazonDynamicAudit`,
      `GetProductTitleReport`,
    ],
  };
  const categoryResponse = await completionFactory(
    "bsrCategoryMatch",
    productCategory,
    clientId,
    options
  );
  // console.log("Category  Response: ", categoryResponse);
  const category = categoryResponse.message;


  const store = {
    titleLengthGuidelines: titleLengthGuidelines,
    category: category,
    titleLength,
    productCategory,
  };
  const titleReports = await parseYamlReport("productTitleReport", store, ymlPath);
  report.push(...titleReports);
}

module.exports = getProductTitleReport;

const { OpenAI } = require("openai");
const fs = require("fs");
const Sentry = require("@sentry/node");
require("dotenv").config();
const { getOpenApiKey } = require("../../models/configuration");

async function getChatGPTResponse(system_prompt, user_prompt, clientId, options) {
  try {
    const client = new OpenAI({
      apiKey: process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY,
      baseURL: 'https://ai.gateway.equalcollective.com/v1',
      model: process.env.OPENAI_MODEL_ID
    });

    const messages = [
      {
        role: "system",
        content: system_prompt,
      },
      {
        role: "user",
        content: user_prompt,
      },
    ];

    // Simple direct tagging system - matches test-dashboard-tags.js output exactly
    const tags = [
      "Jeff",
      "ScrapeGPT",
      "GetChatGPTResponse",
      ...(options.customTags || [])
    ];

    const requestBody = {
      model: options.model || process.env.OPENAI_MODEL_ID || 'gpt-4o',
      messages: messages,
      temperature: options.temperature || 1,
      presence_penalty: options.presence_penalty || 0,
      top_p: options.top_p || 1,
      max_tokens: options.max_tokens || 256,
      metadata: {
        tags: tags
      }
    };

    const completion = await client.chat.completions.create(requestBody);

    result = completion.usage;
    result["message"] = completion.choices[0].message.content;
    result["prompt"] = messages;
    result["metadata"] = requestBody.metadata;
    return result;
  } catch (error) {
    console.error("Error details:", {
      status: error.status,
      message: error.message,
      code: error.code,
      type: error.type
    });

    if (error.status == 400) {
      return {
        message: "",
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      };
    }

    if (error.status == 404) {
      console.error("404 Error - Possible causes:");
      console.error("1. Invalid API endpoint or baseURL");
      console.error("2. Invalid API key");
      console.error("3. Model not available at this endpoint");
      console.error("4. Incorrect API version");
    }

    console.error("Error Stack in getChatGPTResponse:", error.stack);
    Sentry.captureException("Error in getChatGPTResponse", error);
    throw new Error("Error fetching response from ChatGPT: " + error);
  }
}

async function getOpenAICreditBalance() {
  const apiUrl = "https://api.openai.com/v1/credit_balance";
  const { apiKey } = await getOpenApiKey(clientId);
  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });
    console.log(response);

    if (!response.ok) {
      throw new Error("Failed to retrieve balance");
    }

    const data = await response.json();
    return data.amount;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in getOpenAICreditBalance", error);
    throw new Error(`Error fetching OpenAI credit balance: ${error}`);
  }
}

// A function with Amazon Data & About section data  based on which we will call get the prompt template and call the GPT API
async function getKeyword(amazonData, aboutData, clientId) {
  // console.log("DATA FOR KEYWORD:", { amazonData, aboutData });
  try {
    if (!amazonData && !aboutData) {
      throw new Error("Amazon Data or About Data is required");
    }

    let response = null;
    let prompt = "";
    if (amazonData) {
      const amazonPrompt = fs.readFileSync(
        "src/services/scrapeGPT/AmazonPrompt.md",
        "utf8"
      );
      response = await getChatGPTResponse(
        amazonPrompt,
        JSON.stringify(amazonData),
        clientId
      );
      prompt = JSON.stringify(amazonData);
    } else {
      const aboutPrompt = fs.readFileSync(
        "src/services/scrapeGPT/AboutPrompt.md",
        "utf8"
      );
      response = await getChatGPTResponse(
        aboutPrompt,
        JSON.stringify(aboutData),
        clientId
      );
      prompt = JSON.stringify(aboutData);
    }

    // console.log("Keyword Response -> FROM REQUEST FILE:" , response);
    return {
      response,
      prompt,
    };
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in getKeyword", error);
    throw new Error("Error fetching Keyword from ChatGPT: " + error);
  }
}

// This function translate any text to English
async function translateText(text, clientId) {
  try {
    const system_prompt = "Translate the following text to English: ";
    const user_prompt = text;
    const translation = await getChatGPTResponse(
      system_prompt,
      user_prompt,
      clientId
    );
    return translation["message"];
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in translateText", error);
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

// This function will humanize the data points and return same structure with humanized data
async function humanizeData(data, clientId) {
  try {
    const system_prompt = fs.readFileSync(
      "scrapeGPT/HumanizePrompt.md",
      "utf8"
    );
    const user_prompt = JSON.stringify(data);
    const humanizedData = await getChatGPTResponse(
      system_prompt,
      user_prompt,
      clientId
    );
    return humanizedData;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in humanizeData", error);
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}

async function humanizeCompanyName(data, clientId) {
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/CompanyNameHumanizationPrompt.md",
      "utf8"
    );
    const user_prompt = data;
    const humanizedData = await getChatGPTResponse(
      system_prompt,
      user_prompt,
      clientId
    );
    // console.log("FROM REQUEST FILE:",humanizedData)
    return humanizedData;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in humanizeData", error);
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}
async function humanizeProductTitle(data, clientId) {
  try {
    const system_prompt = fs.readFileSync(
      "src/services/scrapeGPT/ProductNameHumanizationPrompt.md",
      "utf8"
    );
    const user_prompt = data;
    const humanizedData = await getChatGPTResponse(
      system_prompt,
      user_prompt,
      clientId
    );
    // console.log("FROM REQUEST FILE:",humanizedData)
    return humanizedData;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in humanizeData", error);
    throw new Error("Error fetching text from ChatGPT: " + error);
  }
}
// Example
async function exampleUsage() {
  try {
    // const amazonData = {
    //   title:
    //     "grabease Baby Toothbrush Finger, Infant Toothbrush, Toddler Toothbrush, Baby Finger Toothbrush, BPA-Free & Phthalate-Free, 1 Pack, Teal",
    //   description:
    //     "INDEPENDENT TOOTHBRUSHING: Introducing our revolutionary toddler toothbrushes 1-2! Designed for independent brushing in 1-2-year-old toddlers, this toothbrush promotes positive oral hygiene. With a dual-sided design, it's perfect for baby toothbrush 12 months and up. The pack includes a double-sided toothbrush for little hands and a gentle silicone finger brush for gum massage. Make dental care easy and fun with our toddler toothbrush, the ideal choice for a child's first toothbrush.     DOUBLE-SIDED TOOTHBRUSH: Get a dazzling smile for your child in no time! Our innovative toothbrush features tapered bristles on both sides, providing maximum tooth contact. Perfect for easily-distracted little ones aged 1-2. Includes a bonus finger toothbrush for infants and kids with sensory or sensitivity issues. Start strong with our versatile brush set for babies 12 months and up.     SOFT BRISTLES & CHOKE PROTECTION: Our patented baby toothbrush ensures a mess-free cleaning experience. With a choke barrier, it prevents over-insertion and gagging. Soft bristles are gentle on tender gums and new baby teeth. A must-have for your 1-year-old's dental care kit, perfect for toddlers transitioning from finger toothbrushes.     EXPERT APPROVED: Specially designed for infants and toddlers, our expert-approved baby toothbrush is perfect for ages 1-2. With top pediatric dentists' seal of approval and endorsements from the American Academy of Pediatric Dentistry, trust our product for your 1-year-old's oral hygiene. Replace every 3-4 months for optimal dental care. Shop with confidence and ensure a safe, effective start to your baby's dental journey.     HOLIDAY GIFT: Our premium baby-led weaning spoons, eating utensils, silicone baby utensils, & infant utensils sets are trusted by industry professionals like pediatric feeding specialists, occupational therapists, pediatricians, & dentists. As featured & endorsed by Romper & Mom.com, Grabease leads the industry in providing safe & innovative products. These make excellent baby stocking stuffers & infant stocking stuffers, ensuring a thoughtful & practical present for your little ones.",
    // };
    // const aboutData = {
    //   "About Data":
    //     "Kore Beauty Co. is here to provide and develop products to solve many of today's issues within the beauty industry. Starting with the many problems with the hair replacement industry. Lace Grip Cosmetic Adhesive was created to give beauty professionals the Ultimate satisfaction of knowing that their clients hair replacement system was secured with the Strongest and Safest Adhesive on the market. Our Adhesive is Water and Oil Resistant, dries quickly, is 100% safe for the skin and is undetectable. ",
    // };

    // const prompt =
    //   "You will be given two things as input by the user - 1. Brand name 2. Product title. Both these things are scraped from an Amazon listing and we are using them to make an audit for that particular listing. In the PPC section of the audit there are two major aspects: 1. 'branded mid-tail keyword' search. Branded: It should always include the name of the brand. Mid-tail: It should be slightly longer, in-depth. It should include what the product is in the keyword & sometimes (if necessary) 1-2 more words to add some more depth to it. 2. 'Non-branded long tail keyword' search. Non-branded: Does not include the brand name. Long-tail: The exact & specific keyword a customer searches for in order to find the product. This is longer and has a good amount of depth (more than mid-tail keywords). Your job is to analyze the information given by the user and the exact: 1. 'branded mid-tail keyword' 2. 'Non-branded long tail keyword.' You do that by first reading and analyzing the information given to you and thinking carefully 'WHAT WOULD AN IDEAL CUSTOMER WHO IS LOOKING FOR THIS PRODUCT SEARCH ON AMAZON TO FIND IT?' The answer to your question is the keyword you have to output. The keyword you give will be searched on the Amazon search bar to see if there are any competitors running ads on this keyword. If there are any competitors targeting this term, then it's really bad for the brand since the customer is looking for the exact product they are offering but someone else is stealing those customers away from them. Here are some examples: User: Brand name - Nike Product Title - Nike mens Free Run 2018 Road Running Assistant: Branded mid-tail keyword - nike's mens running shoe Non-branded long-tail keyword - road running shoes for men User: Brand name - AAVRANI Product Title - AAVRANI Hair & Scalp Recovery Oil - Rosemary, Bond Complex, & Amla Treatment for Damaged Hair, Frizz Control, & Hair Growth - Vegan - 1.69 Fl Oz Assistant: Branded mid-tail keyword - Aavrani hair oil Non-branded long-tail keyword - Vegan hair & scalp recovery oil with rosemary User: Brand name - GhostBed Product Title - GhostBed 3 Inch Cooling Gel Memory Foam Mattress Topper - Waterproof Cover, Protector & Topper in One, Made in USA - Memory Foam Mattress Topper Twin XL Assistant: Branded mid-tail keyword - Ghostbed mattress topper Non-branded long-tail keyword - Waterproof memory foam mattress topper 3 inch Rules to remember: 1. Before giving the keyword, will a consumer write this when searching for this product on Amazon? 2. Make sure the brand name is written like a human would write it, like without capital letters and unusual symbols. 3. Give the output in the EXACT format given in the examples, don't add or remove anything from the format. This is very important.";
    // const user_prompt = {
    //   "Brand Name": "IBENZER",
    //   "Product Title":
    //     'IBENZER Compatible with New 2024 2023 MacBook Air 15 Inch Case M3 A3114 M2 A2941, HardShell Case & KeyboardCover & Type-C Adapter for Mac Air 15.3" Retina Display&Touch ID, CrystalClear, AT15-CYCL+1TC',
    // };
    // const result = await getChatGPTResponse(
    //   prompt,
    //   JSON.stringify(user_prompt),
    //   1
    // );
    // console.log(result);
    const data = "eddy packing co. inc.";

    const response = await humanizeCompanyName(data);
    console.log(response);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error in exampleUsage:", error);
  }
}

// exampleUsage();

module.exports = {
  getChatGPTResponse,
  getKeyword,
  humanizeData,
  humanizeCompanyName,
  humanizeProductTitle,
};

// const data = {
//   asin: "B0C7SBZVF4",
//   revenue: 3045,
//   productName:
//     'IBENZER Compatible with New 2024 2023 MacBook Air 15 Inch Case M3 A3114 M2 A2941, HardShell Case & KeyboardCover & Type-C Adapter for Mac Air 15.3" Retina Display&Touch ID, CrystalClear, AT15-CYCL+1TC',
//   revenueSource: "The 30-day sales number on Amazon",
//   productAmazonURL: "https://www.amazon.com/dp/B0C7SBZVF4",
//   humanizedProspectName: "ibenzer",
//   humanizedProspectProductTitle: "compatible case for new MacBook Air 15 inch",
// };

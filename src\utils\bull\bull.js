const { Queue } = require("bullmq");
const prisma = require("../../database/prisma/getPrismaClient");
require("dotenv").config();
const { connection } = require("./connection");

// Priority lookup maps
const mainQueuePriority = {
  singleJeff: 1,
  singleLexAsin: 2,
  singleLexSeller: 3,
  singleLexReview: 4,
  singleLexViolationDetection: 5,
  singleLexPromptChain: 6,
  reviewScreenshotGeneration: 7,
  bulkJeff: 8,
  bulkLexAsin: 9,
  bulkLexReview: 10,
  bulkLexViolationDetection: 11,
  bulkLexPromptChain: 12,
  lexReviewChecker: 13,
};

const lexQueuePriority = {
  singleJeff: 1,
  singleLexAsin: 2,
  singleLexSeller: 3,
  singleLexReview: 4,
  singleLexViolationDetection: 5,
  singleLexPromptChain: 6,
  reviewScreenshotGeneration: 7,
  bulkLexAsin: 8,
  bulkLexReview: 9,
  bulkLexViolationDetection: 10,
  bulkLexPromptChain: 11,
  bulkJeff: 12,
  lexReviewChecker: 13,
};

/**
 * Determine which queue to use based on options and scriptType.
 */
function getQueueName(scriptType, targetQueue) {
  if (targetQueue === "lex") return "lexQueue";
  if (targetQueue === "main") return "mainQueue";

  // Default routing based on scriptType if targetQueue is not specified
  // Route all lex-related jobs (including prompt chains) to lexQueue
  if (scriptType.toLowerCase().includes("lex")) {
    return "lexQueue";
  }

  return "mainQueue";
}

/**
 * Determine the job priority based on queue and scriptType.
 */
function getPriority(queueName, scriptType) {
  if (queueName === "lexQueue") {
    return lexQueuePriority[scriptType] ?? 8;
  } else {
    return mainQueuePriority[scriptType] ?? 8;
  }
}

/**
 * Adds a job to the appropriate queue with the correct priority.
 * @param {string} scriptType - Job type (e.g., "singleJeff")
 * @param {object} data - Job payload
 * @param {object} options - Options including:
 *     - targetQueue: "main" | "lex"
 *     - every: repeat interval in ms
 */
async function addToQueue(scriptType, data = {}, options = {}) {
  try {
    const queueName = getQueueName(scriptType, options.targetQueue);
    const priority = getPriority(queueName, scriptType);

    const jobQueue = new Queue(queueName, { connection });

    const newJob = await prisma.jobCentral.create({
      data: {
        scriptType,
        priority,
        params: data,
        queueName,
        clientId: data.clientId ?? 1,
      },
    });

    data.id = newJob.id;

    const jobOptions = { priority };

    if (options.every) {
      jobOptions.repeat = { every: options.every };
    }

    await jobQueue.add(scriptType, data, jobOptions);

    console.log(
      `✅ [${queueName}] Added job '${scriptType}' (ID ${newJob.id}) with priority ${priority}`
    );

    return newJob;
  } catch (error) {
    console.error(`❌ Failed to add job '${scriptType}':`, error);
  }
}

module.exports = { addToQueue };

// csvParser.js

const fs = require("fs");
const prisma = require("../database/prisma/getPrismaClient");
const csvtojson = require("csvtojson");
const Sentry = require("@sentry/node");
const { TARGET_URL } = require("../services/scrapeAmazon/constant");
async function processCSV(csvFilePath, clientId, job) {
  try {
    // Async / await usage
    const jsonArray = await csvtojson().fromFile(csvFilePath);

    for (let i = 0; i < jsonArray.length; i++) {
      console.log("Processing row:", i + 1);
      await processJsonData(jsonArray[i], clientId, job);
    }

    console.log(
      "++++++++++++CSV Parsing and Insertion complete++++++++++++++++++"
    );
  } catch (e) {
    console.log("Error in processCSV", e);
  }
}

async function processJsonData(row, clientId, job) {
  // console.log("Row data:", row);
  let secondName = row["Decision Maker Last Name"] || row["Last Name"] || "";
  let position = row["Job Title"] || "";
  let email =
    row["Personal Email 1"] ||
    row["Email"] ||
    row["Office Email"] ||
    "No Email-Id Found";
  const firstName = row["Decision Maker First Name"] || row["First Name"] || "";

  const name =
    row["Seller Business Name"]?.toLowerCase() ||
    row["Seller Name"]?.toLowerCase() ||
    "";
  const website = row?.["Website"] || row?.["Email"]?.split("@")[1] || "";
  const asin = row?.["ASIN"] || "";
  const prospectDetails = {
    numberOfBrandsSold: parseInt(row["Number of Brands Sold"]) || 0,
    bestSellingProductURL: row["Best Selling Product URL"] || "",
    bestSellingProductPrice: parseInt(row["Best Selling Product Price"]) || 0.0,
    storeFrontURL: row["Seller Storefront Link"] || "",
    asin,
  };
  const competitorDetails = {
    productAmazonURL: row["Competitor Product Link"] || "",
  };

  const sellerDetails = row;
  try {
    const formatSearchKeyword = name
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "+")
      .replace(/^-+|-+$/g, "");

    let searchUrl = name? `${TARGET_URL}/s?k=${formatSearchKeyword}` : "N/A";
    let company = null;
    if (prospectDetails.bestSellingProductURL) {
      company = await prisma.company.findFirst({
        where: {
          productUrl: prospectDetails.bestSellingProductURL,
        },
      });
    }
    if (!company && !prospectDetails.bestSellingProductURL && prospectDetails.storeFrontURL) {
      company = await prisma.company.findFirst({
        where: {
          storeFrontURL: prospectDetails.storeFrontURL,
        },
      });
    }
    if (!company && !prospectDetails.bestSellingProductURL && !prospectDetails.storeFrontURL && searchUrl) {
      company = await prisma.company.findFirst({
        where: {
          searchUrl,
        },
      });
    }

    if (!company) {
      company = await prisma.company.create({
        data: {
          name,
          website,
          clientId,
          jobId: job.id,
          searchUrl,
          storeFrontURL: prospectDetails.storeFrontURL,
          productUrl: prospectDetails.bestSellingProductURL,
        },
      });
    }
    const campaignName = await prisma.campaign.findFirst({
      where: {
        id:job.campaignId
      }
    })

    await prisma.outputData.create({
      data: {
        companyName: company.name,
        firstName,
        secondName,
        position,
        email,
        website,
        companyId: company.id,
        jobId: job.id,
        campaignName:campaignName.campaign,
        prospectDetails: prospectDetails,
        sellerDetails: sellerDetails,
        competitorDetails:competitorDetails,
      },
    });
  } catch (e) {
    console.log("Error in inserting company data", e);
  }
}

module.exports = { processCSV, processJsonData };

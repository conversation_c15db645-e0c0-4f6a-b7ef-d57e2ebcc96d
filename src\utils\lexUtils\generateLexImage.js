const { getBrowser, closePages, closeBrowsers } = require("../puppeteer/browserHelper");
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const { uploadImage } = require("../../services/aws/s3");
const prisma = require("../../database/prisma/getPrismaClient");
const { getScraperKeys } = require("../../models/configuration")


const bucketName = "eq-lex";
const imageFolder = "lex_images/";

async function fetchImageAsBase64(url) {
  const response = await axios.get(url, {
    responseType: "arraybuffer",
  });
  return Buffer.from(response.data, "binary").toString("base64");
}

async function generateReviewImage(reviewUrl, clientId, attempt = 1) {
  console.log(`Starting screenshot for: ${reviewUrl} | Attempt ${attempt}`);
  let page;
  try {
    const { SCRAPER_API_KEY } = await getScraperKeys(clientId);
    const browser = await getBrowser();
    page = await browser.newPage();
    const scraperApiUrl = `http://api.scraperapi.com?api_key=${SCRAPER_API_KEY}&url=${encodeURIComponent(reviewUrl)}`;

    await page.goto(scraperApiUrl, {
      waitUntil: "domcontentloaded",
      timeout: 60000,
    });

    await page.setViewport({ width: 1080, height: 920, deviceScaleFactor: 2 });

    await page.evaluate(() => {
      const classesToRemove = [
        ".review-comments",
        ".review-image-tile"
      ];

      classesToRemove.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });
    });


    const reviewBox = await page.$("#cm_cr-review_list");
    const reviewText = await page.$(".review-text ");
    const reviewHeading = await page.$(".review-title-content");
    const reviewDate = await page.$(".review-date");
    const reviewData = await page.$(".review-data");

    if (reviewBox) {
      const box = await reviewBox.boundingBox();
      const textBox = await reviewText?.boundingBox() || { width: 0 };
      const headingBox = await reviewHeading?.boundingBox() || { width: 0 };
      const dateBox = await reviewDate?.boundingBox() || { width: 0 };
      const dataBox = await reviewData?.boundingBox() || { width: 0, height: 0 };

      const elements = [textBox, headingBox, dateBox, dataBox];
      const lastElement = elements.reduce((last, current) => {
        if (!current) return last;
        if (!last) return current;
        return (current.y + current.height) > (last.y + last.height) ? current : last;
      }, null);

      const maxWidth = Math.max(textBox.width, headingBox.width, dateBox.width);
      const height = lastElement ? (lastElement.y + lastElement.height - box.y) : box.height;

      const reviewImageBuffer = await page.screenshot({
        encoding: "binary",
        clip: {
          x: box.x - 10,
          y: box.y,
          width: maxWidth + 20,
          height: height + 10,
        },
      });

      console.log("Review image captured successfully");
      await page.close();
      return reviewImageBuffer;
    } else {
      console.log("Review box not found.");
      await page.close();
      return null;
    }
  } catch (error) {
    console.error("Error in generateReviewImage:", error.message);
    if (page) await page.close();
    if (attempt < 3) {
      // Generate random delay between 3-6 seconds
      const minDelay = 3000; // 3 seconds
      const maxDelay = 6000; // 6 seconds
      const randomDelay = Math.floor(
        Math.random() * (maxDelay - minDelay + 1) + minDelay
      );
      
      console.log(`Retrying screenshot in ${randomDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, randomDelay));
      return generateReviewImage(reviewUrl, clientId, attempt + 1);
    }
    return null;
  }
}



async function generateCombinedImagePuppeteer(
  reviewId,
  violationTagImageUrls,
  reviewImageBuffer,
  attempt = 1
) {
  console.log(`Generating combined image | Attempt ${attempt}`);
  let page;
  try {
    const browser = await getBrowser();
    page = await browser.newPage();

    const reviewImageBase64 = reviewImageBuffer.toString("base64");

    const violationImageHtml = violationTagImageUrls
      .map(
        (imageUrl, index) => `
        <div class="image-container">
          <img src="${imageUrl}" alt="Violation Tag ${index + 1}" />
        </div>`
      )
      .join("");

    const htmlContent = `
      <html>
        <head>
          <style>
            body {
              margin: 0;
              padding: 0;
              background: white;
              font-family: Arial, sans-serif;
            }
            .page-wrapper {
              padding: 80px;
              background: white;
              box-sizing: border-box;
            }
            h2 {
              font-size: 40px;
              margin: 20px 0;
            }
            .section {
              margin-bottom: 50px;
              width: 100%;
            }
            .image-container {
              border: 8px solid red;
              padding: 12px;
              background: #fdfdfd;
              margin-bottom: 30px;
              width: 100%;
              box-sizing: border-box;
            }
            .container {
              max-width: 900px;
              margin: 0 auto;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            img {
              max-width: 100%;
              height: auto;
              display: block;
            }
          </style>
        </head>
        <body>
          <div class="page-wrapper">
            <div class="container">
              <div class="section">
                <h2>Review Screenshot</h2>
                <div class="image-container">
                  <img src="data:image/png;base64,${reviewImageBase64}" />
                </div>
              </div>
              <div class="section">
                <h2>Community Guideline Violation</h2>
                ${violationImageHtml}
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    await page.setContent(htmlContent, { waitUntil: "networkidle0" });

    await page.setViewport({
      width: 1080,
      height: 800, // temporary value; fullPage will override this
      deviceScaleFactor: 2,
    });

    const imageName = `${reviewId}.png`;
    const lexImageBuffer = await page.screenshot({
      type: "png",
      fullPage: true,
    });

    const image = await uploadImage(
      lexImageBuffer,
      imageName,
      imageFolder,
      bucketName
    );

    const lexImageUrl = `https://${bucketName}.s3.ap-south-1.amazonaws.com/${imageFolder}${imageName}`;
    await page.close();
    return lexImageUrl;
  } catch (error) {
    console.error("Error in generateCombinedImagePuppeteer:", error.message);
    if (page) await page.close();
    if (attempt < 3) {
      console.log("Retrying combined image generation...");
      return generateCombinedImagePuppeteer(
        reviewId,
        violationTagImageUrls,
        reviewImageBuffer,
        attempt + 1
      );
    }
    return null;
  }
}



async function generateLexImage(review, clientId) {
  try {
    // console.log({ review });

    let reviewData = await prisma.lexImageGenReview.findUnique({
      where: {
        id: review.revId,
      }
    });
    // console.log({ lexImageUrl });
    // if (reviewData.imageUrl) {
    //   console.log("Image already exists:", reviewData.imageUrl);
    //   return reviewData.imageUrl;
    // }
    const screenshotSuccess = await generateReviewImage(review.reviewUrl, 1);

    const violationTagImageUrls = await prisma.lexViolationTag.findMany({
      where: {
        id: {
          in: review.violationTagId,
        },
      },
      select: {
        imageUrl: true,
      },
    });
    const imageUrls = violationTagImageUrls.map((tag) => tag.imageUrl);
    // console.log({ imageUrls });

    if (screenshotSuccess) {
      const lexImageUrl = await generateCombinedImagePuppeteer(
        reviewData.reviewId,
        imageUrls,
        screenshotSuccess
      );
      console.log("Combined image URL:", lexImageUrl);
      if (lexImageUrl) {
        await prisma.lexImageGenReview.update({
          where: { id: review.revId },
          data: { imageUrl: lexImageUrl, status: "COMPLETED" },
        });
      }
      return lexImageUrl;
    } else {
      console.log("Skipping image combination due to screenshot failure.");
      return null;
    }
  } catch (error) {
    console.error("Error in example function:", error);
    return null;
  }
}

// Test function to check if generateReviewImage works
// async function testGenerateReviewImage(reviewUrl, clientId = 1) {
//   console.log(`📸 Testing generateReviewImage for: ${reviewUrl}`);

//   try {
//     const screenshotBuffer = await generateReviewImage(reviewUrl, clientId, 1);

//     if (screenshotBuffer) {
//       console.log(
//         `✅ Screenshot generated successfully! Buffer size: ${screenshotBuffer.length} bytes`
//       );

//       // Save to file for testing
//       const testDir = path.join(__dirname, "../../test_screenshots");

//       if (!fs.existsSync(testDir)) {
//         fs.mkdirSync(testDir, { recursive: true });
//       }

//       const filename = `test_screenshot_${Date.now()}.png`;
//       const filepath = path.join(testDir, filename);

//       fs.writeFileSync(filepath, screenshotBuffer);
//       console.log(`💾 Screenshot saved to: ${filepath}`);

//       return {
//         success: true,
//         message: "Screenshot generated successfully",
//         bufferSize: screenshotBuffer.length,
//         savedTo: filepath,
//       };
//     } else {
//       console.error(`❌ Failed to generate screenshot for: ${reviewUrl}`);
//       return {
//         success: false,
//         message: "Failed to generate screenshot",
//         reviewUrl: reviewUrl,
//       };
//     }
//   } catch (error) {
//     console.error("❌ Error in testGenerateReviewImage:", error);
//     console.error("Error Stack:", error.stack);
//     throw error;
//   }
// }

module.exports = {
  generateLexImage,
  generateReviewImage,
  generateCombinedImagePuppeteer,
};

// testGenerateReviewImage(
//   "https://www.amazon.com/gp/customer-reviews/R3EREIGOOQPG5U"
// );

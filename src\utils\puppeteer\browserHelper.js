const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const getProxyRoyalPass = require("../multicountry/getProxyPassRoyal");
const path = require("path");

require("dotenv").config();
puppeteer.use(StealthPlugin());

let proxyBrowserInstance = null;
let normalBrowserInstance = null;

// Determine the environment: isLocal is true if BROWSER_ENV is "test"
const isLocal = process.env.BROWSER_ENV === "test";

/**
 * Creates a new browser instance based on the type and environment.
 * @param {string} type - "proxy" or "normal"
 * @param {boolean} isLocal - Whether running in local environment
 * @returns {Promise<Browser>} The Puppeteer browser instance
 */
async function createInstance(type, isLocal) {
  const CACHE_DIR = path.resolve(__dirname, "puppeteer-cache");
  const args = [
    `--disk-cache-dir=${CACHE_DIR}`,
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    ...(type === "proxy" ? ["--proxy-server=geo.iproyal.com:12321"] : []),
  ];

  return puppeteer.launch({
    args,
    defaultViewport: null,
    executablePath: undefined,
    headless: true,
  });
}

/**
 * Returns a Puppeteer browser instance based on the environment and type.
 * Creates a new instance if none exists.
 * @param {string} type - "proxy" for proxy-enabled browser, "normal" for default browser
 * @returns {Promise<Browser>} The Puppeteer browser instance
 */
async function getBrowser(type = "normal") {
  try {
    if (type === "proxy") {
      if (
        !proxyBrowserInstance ||
        proxyBrowserInstance.isConnected() === false
      ) {
        console.log(
          `Launching a new proxy-enabled browser instance (${
            isLocal ? "local" : "prod"
          })...`
        );
        proxyBrowserInstance = await createInstance("proxy", isLocal);
        return proxyBrowserInstance;
      } else {
        console.log("using existing proxy browser...");
        return proxyBrowserInstance;
      }
    } else if (type === "normal") {
      if (
        !normalBrowserInstance ||
        normalBrowserInstance.isConnected() === false
      ) {
        console.log(
          `Launching a new normal browser instance (${
            isLocal ? "local" : "prod"
          })...`
        );
        normalBrowserInstance = await createInstance("normal", isLocal);
        return normalBrowserInstance;
      } else {
        console.log("using existing normal browser...");
        return normalBrowserInstance;
      }
    } else {
      throw new Error(`Invalid browser type: ${type}`);
    }
  } catch (error) {
    console.log("Error creating browser:", error);
    // Reset the instance if there was an error
    if (type === "proxy") {
      proxyBrowserInstance = null;
    } else {
      normalBrowserInstance = null;
    }
    throw error;
  }
}

/**
 * Closes all open pages in a Puppeteer browser instance.
 * @param {Browser} browserInstance - The Puppeteer browser instance
 */
async function closePages(browserInstance) {
  if (browserInstance && browserInstance.isConnected()) {
    try {
      const pages = await browserInstance.pages();
      for (const page of pages) {
        try {
          if (!page.isClosed()) {
            await page.close();
          }
        } catch (err) {
          console.error("Error closing individual page:", err.message);
        }
      }
      console.log("All pages closed.");
    } catch (err) {
      console.error("Error getting pages from browser:", err.message);
    }
  } else {
    console.log(
      "Browser instance is null or disconnected, skipping page closure."
    );
  }
}

/**
 * Closes the Puppeteer browser instances if they exist.
 */
async function closeBrowsers() {
  try {
    console.log("Closing the browser instance...");

    if (normalBrowserInstance) {
      try {
        if (normalBrowserInstance.isConnected()) {
          await closePages(normalBrowserInstance);
          await normalBrowserInstance.close();
        }
        normalBrowserInstance = null;
        console.log("Closed Normal Browser Instance...");
      } catch (error) {
        console.error("Error closing normal browser:", error.message);
        normalBrowserInstance = null; // Reset even if close failed
      }
    }

    if (proxyBrowserInstance) {
      try {
        if (proxyBrowserInstance.isConnected()) {
          await closePages(proxyBrowserInstance);
          await proxyBrowserInstance.close();
        }
        proxyBrowserInstance = null;
        console.log("Closed Proxy Browser Instance...");
      } catch (error) {
        console.error("Error closing proxy browser:", error.message);
        proxyBrowserInstance = null; // Reset even if close failed
      }
    }

    console.log("****************************************");
  } catch (error) {
    console.log("ERROR closing browser:", error);
    // Reset instances in case of any error
    normalBrowserInstance = null;
    proxyBrowserInstance = null;
  }
}

/**
 * Safely closes a specific page
 * @param {Page} page - The Puppeteer page instance to close
 */
async function closePage(page) {
  if (page && !page.isClosed()) {
    try {
      await page.close();
      console.log("Page closed successfully");
    } catch (err) {
      console.error("Error closing page:", err.message);
    }
  }
}

module.exports = { getBrowser, closeBrowsers, closePages, closePage };

const prisma = require("../../database/prisma/getPrismaClient");
const { isValidS3Link } = require("../../services/aws/s3");
const getRatingAnalysis = require("../amazonAuditUtils/getRatingAnalysis");
const createAndUpdateUTMUrl = require("../utmHelpers/createAndUpdateUTMUrl");
const { updateJeffDataAnalysisFromProcessFinalData } = require("../jeffDataAnalysisUtils");

async function processFinalData(csvData, clientId, auditPdf) {
  function formatRevenue(revenue) {
    const formatter = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    return formatter.format(revenue);
  }
  const client = await prisma.user.findUnique({
    where: {
      id: clientId,
    },
    select: {
      signature: true,
      logo: true,
      mbLogo: true,
      tableLogo: true,
      ctaLink: true,
      name: true,
    },
  });

  const ratingAnalysis = getRatingAnalysis(csvData);

  const redirectAuditWebLink = await createAndUpdateUTMUrl({
    url: csvData.productSlug
      ? `https://www.equalcollective.com/jeff/audit/${csvData.productSlug}`
      : "",
    clientName: client.name,
    sellerId: csvData.companyName,
    type: "audit",
    campaign: csvData.campaignName,
    email: csvData.email || "",
  });
  let redirectAuditPdfLink = "N/A";
  if (auditPdf) {
   redirectAuditPdfLink = await createAndUpdateUTMUrl({
      url:
      csvData.productSlug &&
      (await isValidS3Link(
        `https://eq--assets.s3.ap-south-1.amazonaws.com/pdfs/${csvData.productSlug}.pdf`
      ))
      ? `https://eq--assets.s3.ap-south-1.amazonaws.com/pdfs/${csvData.productSlug}.pdf`
      : "",
      clientName: client.name,
      sellerId: csvData.companyName,
      type: "pdf",
      campaign: csvData.campaignName,
      email: csvData.email || "",
    });
  }

  csvData["finalData"] = {
    "Seller name": csvData.companyName || "",
    "Seller name (humanized)":
      csvData.prospectDetails.humanizedProspectName || "",
    "Seller Website": csvData.website || "",
    "Storefront URL": csvData.sellerDetails["Seller Storefront Link"] || "",
    "Prospect First Name": csvData.firstName || "",
    "Prospect Second Name": csvData.secondName || "",
    "Job Title": csvData.position || "",
    Email: csvData.email || "",
    "Prospect LinkedIn URL": csvData.sellerDetails.prospectLinkedinURL || "",
    "Seller company's LinkedIn URL":
      csvData.sellerDetails.companyLinkedinURL || "",
    "Competitor Email": csvData.mailData || "",
    "BSR Category":
      csvData.category &&
      csvData.category.length > 0 &&
      csvData?.category[0]?.category
        ? csvData?.category[0]?.category
        : "",
    "Prospect product URL": csvData.prospectDetails.productAmazonURL || "",
    "Competitor Product URL": csvData.competitorDetails.productAmazonURL || "",
    "Audit webpage link": redirectAuditWebLink,
    "Audit PDF link": redirectAuditPdfLink,
    "Amazon search URL": csvData.prospectDetails.productAmazonURL || "",
    "Prospect Product Title (Humanized)":
      csvData.prospectDetails.humanizedProspectProductTitle || "",
    "Competitor Product Title (Humanized)":
      csvData.competitorDetails.humanizedCompProductTitle || "",
    "Revenue difference monthly":
      formatRevenue(csvData.revenueDifference) || "0",
    "Revenue difference yearly":
      formatRevenue(csvData.revenueDifference * 12) || "0",
    "Competitor brand name (humanized)":
      csvData.competitorDetails.humanizedCompCompanyName || "",
    "Prospect revenue": formatRevenue(csvData.prospectDetails.revenue) || "0",
    "Competitor revenue":
      formatRevenue(csvData.competitorDetails.revenue) || "0",
    "Annual Competitor Revenue":
      formatRevenue(csvData.competitorDetails?.revenue * 12 || 0) || 0,
    "Revenue Source": csvData.prospectDetails.revenueSource || "",
    "Number of optimization": Object.keys(csvData.auditReport).length || 0,
    "Revenue source": csvData.prospectDetails.revenueSource || "",
    "User Prompt": JSON.stringify(csvData.userPrompt),
    "Prospect Details": JSON.stringify(csvData.prospectDetails),
    "Competitor Details": JSON.stringify(csvData.competitorDetails),
    "Amazon Audit": JSON.stringify(csvData.amazonAudit),
    "Audit Report": JSON.stringify(csvData.auditReport),
    "1 Star Reviews": ratingAnalysis?.oneStarReview || 0,
    "2 Star Reviews": ratingAnalysis?.twoStarReview || 0,
    "1 and 2 Star Reviews":
      ratingAnalysis?.oneStarReview + ratingAnalysis?.twoStarReview || 0,
    "Total Number Of Ratings": ratingAnalysis?.totalNoOfRatings || 0,
    "Number Of Stars": ratingAnalysis?.noOfStars || 0,
    "Number Of Stars Comes As": ratingAnalysis?.noOfStarsComesAs || 0,
    "Goal for Number of stars": ratingAnalysis?.goalForNumberOfStars || 0,
    "Goal for Number of stars 'comes as' ":
      ratingAnalysis?.goalForNumberOfStarsComesAs || 0,
    "Number of 5* ratings needed": ratingAnalysis?.numbOf5StarNeeded || 0,
    "Minimum number of 1* & 2* to be removed":
      ratingAnalysis?.lowStarRemovalResult,
    "Page Image":
      csvData.productSlug &&
      (await isValidS3Link(
        `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_page_image.png`
      ))
        ? `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_page_image.png`
        : "",
    "Product Image":
      csvData.productSlug &&
      (await isValidS3Link(
        `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_product_image.png`
      ))
        ? `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_product_image.png`
        : "",
    "Audit Mail Image":
      csvData.productSlug &&
      (await isValidS3Link(
        `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_audit_image.png`
      ))
        ? `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${csvData.productSlug}_audit_image.png`
        : "",
    "Case Studies": JSON.stringify(csvData.caseStudies),
    ID: csvData.id,
    "Prompt Tokens": csvData.promptTokens || 0,
    "Completion Tokens": csvData.completionTokens || 0,
    "Homepage Data Status": csvData.homepageDataStatus || "not-found",
    "Prompt Template": csvData.promptTemplate || "",
    "GPT Details": csvData.gptDetails || "",
    "Input Price": csvData.inputPrice || 0.0,
    "Output Price": csvData.outputPrice || 0.0,
    "Comp Key Prompt": csvData.compKeyPrompt || "",
    "Search Keyword": csvData.searchKeyword || "",
    "Company Slug": csvData.companySlug || "",
    "Product Slug": csvData.productSlug || "",
    "Job ID": csvData.jobId,
    "Qualification Status": csvData.qualificationStatus || "",
    "Company ID": csvData.companyId,
    "Created At": csvData.createdAt,
    "Updated At": csvData.updatedAt,
    "Client Signature": client.signature || "",
    "Client Logo": client.logo || "",
    "Client MB Logo": client.mbLogo || "",
    "Client Tablet Logo": client.tableLogo || "",
    "Client CTA Link": client.ctaLink || "",
  };

  // Update JeffDataAnalysis table whenever finalData is processed
  try {
    await updateJeffDataAnalysisFromProcessFinalData(csvData);
  } catch (error) {
    console.error("Error updating JeffDataAnalysis table:", error);
    // Don't throw error to avoid breaking the main process
  }
}

module.exports = processFinalData;

/**
 * Auto Scaling Worker for bulkJeff Jobs
 *
 * This worker automatically scales Azure VM instances based on the number of pending bulkJeff jobs.
 * It runs every hour and only controls specific instances defined in the configuration.
 *
 * SCALING LOGIC:
 * - Only scales when there are more than 1 bulkJeff job (PENDING + IN_PROGRESS) (configurable threshold)
 * - Simple scaling: 2 jobs = 2 servers, 3 jobs = 3 servers, etc.
 * - Always maintains 2 instances (1 for API + 1 for Lex)
 * - Maximum of 5 total instances (2 always-on + 3 extra for jeff)
 *
 * INSTANCE MANAGEMENT:
 * - Only controls Azure VMs listed in availableInstances array
 * - Ignores other VMs (API, Lex, etc.)
 * - Respects cooldown periods to prevent rapid scaling
 * - Prevents duplicate scaling actions within 5 minutes
 *
 * CONFIGURATION:
 * - scalingThreshold: Minimum bulkJeff jobs to trigger scaling (default: 1)
 * - availableInstances: Array of Azure VM names this worker can control
 * - minInstances: Always keep running (2 = API + Lex)
 * - maxInstances: Maximum total instances (5 = 2 always-on + 3 extra)
 * - cooldownMinutes: Wait time before scaling down (default: 10)
 *
 * NOTIFICATIONS:
 * - Sends Slack notifications for all scaling actions
 * - Uses TECH_TEAM_SLACK_URL environment variable
 *
 */

const cron = require("node-cron");
const { PrismaClient } = require("@prisma/client");
const { JobStatus } = require("@prisma/client");
const {
  listInstances,
  startInstance,
  stopInstance,
} = require("../services/azure/azureInstanceManager");
const { sendMessageToSlack } = require("../utils/slack");

const prisma = new PrismaClient();

// Configuration for auto-scaling
const SCALING_CONFIG = {
  // Focus only on bulkJeff jobs
  jobType: "bulkJeff",

  // Scaling threshold - only scale when there are more than this many bulkJeff jobs
  scalingThreshold: 1,

  // Instance management for bulkJeff scaling
  instances: {
    // Available Azure VM names for jeff scaling (4 extra servers)
    // TODO: Update these with actual Azure VM names from your resource group
    availableInstances: [
      "jeff-worker-2",
      "jeff-worker-3",
      "jeff-worker-4",
      "jeff-worker-5",
    ],
    // Minimum instances to keep running (1 for API + 1 for Lex = 2 always on)
    minInstances: 2,
    // Maximum instances to scale to (2 always on + 4 extra = 6 total)
    maxInstances: 5,
    // Cooldown period in minutes before scaling down (to prevent rapid scaling)
    cooldownMinutes: 30,
  },
  // Notification settings
  notifications: {
    enabled: true,
    slackWebhookUrl: process.env.TECH_TEAM_SLACK_URL,
  },
};

class AutoScalingWorker {
  constructor() {
    this.isRunning = false;
    this.lastScalingAction = new Map(); // Track last scaling action per instance
    this.scalingHistory = []; // Track scaling history for debugging
    this.lastScalingDecision = null; // Track the last scaling decision to prevent duplicates
    this.lastScalingTimestamp = null; // Track when the last scaling action was taken
  }

  async start() {
    console.log("🚀 Starting Auto Scaling Worker...");

    // Run every 30 minute to check job queues and scale accordingly
    cron.schedule("*/30 * * * *", async () => {
      if (this.isRunning) {
        console.log("⏳ Auto scaling worker already running, skipping...");
        return;
      }

      try {
        this.isRunning = true;
        await this.performAutoScaling();
      } catch (error) {
        console.error("❌ Error in auto scaling worker:", error);
        await this.sendNotification(`❌ Auto scaling error: ${error.message}`);
      } finally {
        this.isRunning = false;
      }
    });

    console.log("✅ Auto Scaling Worker scheduled to run every hour");
  }

  async performAutoScaling() {
    console.log("🔄 Starting auto scaling check...");

    try {
      // Get current job counts and processing times by type
      const { jobCounts, processingTimes } = await this.getJobCountsByType();
      const bulkJeffCount = jobCounts[SCALING_CONFIG.jobType] || 0;

      console.log("📊 Current job counts:", jobCounts);
      console.log("⏱️  Total processing times (minutes):", processingTimes);

      // Get current instance states
      let instances = (await listInstances()) || [];
      instances = instances?.filter((inst) =>
        SCALING_CONFIG.instances.availableInstances.includes(inst.InstanceId)
      );
      const runningInstances = instances.filter(
        (inst) => inst.State === "running"
      );
      const stoppedInstances = instances.filter(
        (inst) => inst.State === "stopped"
      );

      console.log(
        `🖥️  Current Azure VMs - Running: ${runningInstances.length}, Stopped: ${stoppedInstances.length}`
      );

      // Determine if we need to scale up or down
      const scalingDecision = this.calculateScalingDecision(
        processingTimes,
        runningInstances.length
      );

      // Check if this is a duplicate scaling decision (same action, same reason within 5 minutes)
      const now = new Date();
      const isDuplicate =
        this.lastScalingDecision &&
        this.lastScalingDecision.action === scalingDecision.action &&
        this.lastScalingDecision.reason === scalingDecision.reason &&
        this.lastScalingTimestamp &&
        now - this.lastScalingTimestamp < 5 * 60 * 1000; // 5 minutes

      if (isDuplicate) {
        console.log(
          `⏸️  Skipping duplicate scaling action: ${scalingDecision.action} - ${scalingDecision.reason}`
        );
        this.logScalingHistory(
          jobCounts,
          processingTimes,
          runningInstances.length,
          {
            action: "none",
            reason: `Skipped duplicate scaling action: ${scalingDecision.action}`,
          }
        );
        return;
      }

      // Only scale UP if there are more than the threshold number of bulkJeff jobs (pending + in progress)
      if (
        scalingDecision.action === "scale-up" &&
        bulkJeffCount > SCALING_CONFIG.scalingThreshold
      ) {
        console.log(
          `📈 Scaling UP triggered: ${bulkJeffCount} bulkJeff jobs (pending + in progress) > threshold (${SCALING_CONFIG.scalingThreshold})`
        );
        await this.scaleUp(scalingDecision.reason, stoppedInstances);
        this.lastScalingDecision = scalingDecision;
        this.lastScalingTimestamp = now;
      } else if (
        scalingDecision.action === "scale-down" &&
        scalingDecision.reason
      ) {
        // Always allow scaling down regardless of threshold
        console.log(
          `📉 Scaling DOWN triggered: ${bulkJeffCount} bulkJeff jobs (pending + in progress), can reduce instances`
        );
        await this.scaleDown(scalingDecision.reason, runningInstances);
        this.lastScalingDecision = scalingDecision;
        this.lastScalingTimestamp = now;
      } else if (bulkJeffCount <= SCALING_CONFIG.scalingThreshold) {
        console.log(
          `⏸️  Only ${bulkJeffCount} bulkJeff job(s) (pending + in progress) - no scaling up needed (threshold: >${SCALING_CONFIG.scalingThreshold})`
        );
      } else {
        console.log(
          "✅ No scaling action needed - current capacity is appropriate"
        );
      }

      // Log scaling history
      this.logScalingHistory(
        jobCounts,
        processingTimes,
        runningInstances.length,
        scalingDecision
      );
    } catch (error) {
      console.error("❌ Error during auto scaling:", error);
      throw error;
    }
  }

  async getJobCountsByType() {
    // Get bulkJeff jobs that are either pending or in progress (both need processing capacity)
    const bulkJeffJobs = await prisma.jobCentral.count({
      where: {
        scriptType: SCALING_CONFIG.jobType,
        status: {
          in: [JobStatus.PENDING, JobStatus.IN_PROGRESS],
        },
      },
    });

    const jobCounts = {
      [SCALING_CONFIG.jobType]: bulkJeffJobs,
    };

    const processingTimes = {
      [SCALING_CONFIG.jobType]: bulkJeffJobs, // Each bulkJeff job = 1 unit of processing time
    };

    return { jobCounts, processingTimes };
  }

  calculateScalingDecision(processingTimes, runningInstanceCount) {
    const bulkJeffCount = processingTimes[SCALING_CONFIG.jobType] || 0;

    console.log(
      `📊 bulkJeff jobs: ${bulkJeffCount}, Running instances: ${runningInstanceCount}`
    );

    // Simple scaling logic: 2 jobs = 2 servers, 3 jobs = 3 servers, etc.
    // But we need to account for the 2 always-on servers (API + Lex)
    const requiredInstances = Math.min(
      bulkJeffCount + 2,
      SCALING_CONFIG.instances.maxInstances
    );

    console.log(
      `🎯 Required instances: ${requiredInstances} (${bulkJeffCount} bulkJeff + 2 always-on)`
    );

    if (runningInstanceCount < requiredInstances) {
      // Need to scale up
      const instancesToAdd = requiredInstances - runningInstanceCount;
      return {
        action: "scale-up",
        reason: `${bulkJeffCount} bulkJeff jobs require ${requiredInstances} total instances (${bulkJeffCount} + 2 always-on), currently running ${runningInstanceCount}, need to add ${instancesToAdd} instance(s)`,
        jobType: SCALING_CONFIG.jobType,
        bulkJeffCount,
        requiredInstances,
        instancesToAdd,
      };
    } else if (
      runningInstanceCount > requiredInstances &&
      runningInstanceCount > SCALING_CONFIG.instances.minInstances
    ) {
      // Can scale down (but not below minimum)
      const instancesToRemove = runningInstanceCount - requiredInstances;
      return {
        action: "scale-down",
        reason: `${bulkJeffCount} bulkJeff jobs require ${requiredInstances} total instances, currently running ${runningInstanceCount}, can remove ${instancesToRemove} instance(s)`,
        jobType: SCALING_CONFIG.jobType,
        bulkJeffCount,
        requiredInstances,
        instancesToRemove,
      };
    } else {
      return {
        action: "none",
        reason: `Current capacity (${runningInstanceCount} instances) matches required capacity (${requiredInstances} instances) for ${bulkJeffCount} bulkJeff jobs`,
      };
    }
  }

  async scaleUp(reason, stoppedInstances) {
    console.log(`📈 Scaling UP: ${reason}`);

    if (stoppedInstances.length === 0) {
      console.log("⚠️ No stopped instances available for scaling up");
      return;
    }

    // Extract the number of instances needed from the reason
    const instancesToAdd = reason.match(/need to add (\d+) instance/)?.[1] || 1;
    const instancesToStart = Math.min(
      parseInt(instancesToAdd),
      stoppedInstances.length
    );

    // Filter to only include VMs from our availableInstances list
    const controllableStoppedInstances = stoppedInstances.filter((instance) =>
      SCALING_CONFIG.instances.availableInstances.includes(instance.InstanceId)
    );

    if (controllableStoppedInstances.length === 0) {
      console.log(
        "⚠️ No controllable stopped Azure VMs available for scaling up"
      );
      return;
    }

    const instancesToStartCount = Math.min(
      instancesToStart,
      controllableStoppedInstances.length
    );
    console.log(
      `🚀 Starting ${instancesToStartCount} Azure VM(s) from ${controllableStoppedInstances.length} available controllable stopped VMs`
    );

    let startedCount = 0;
    for (let i = 0; i < instancesToStartCount; i++) {
      const instanceToStart = controllableStoppedInstances[i];
      console.log(
        `🚀 Starting Azure VM ${i + 1}/${instancesToStartCount}: ${
          instanceToStart.InstanceId
        } (${instanceToStart.Name})`
      );

      try {
        const result = await startInstance(instanceToStart.InstanceId);

        if (result.success) {
          const message = `✅ Auto-scaling: Started Azure VM ${instanceToStart.InstanceId} (${instanceToStart.Name}) - ${reason}`;
          console.log(message);
          await this.sendNotification(message);

          // Record scaling action
          this.lastScalingAction.set(instanceToStart.InstanceId, {
            action: "started",
            timestamp: new Date(),
            reason,
          });

          startedCount++;
        } else {
          const errorMessage = `❌ Failed to start Azure VM ${instanceToStart.InstanceId}: ${result.message}`;
          console.error(errorMessage);
          await this.sendNotification(errorMessage);
        }
      } catch (error) {
        const errorMessage = `❌ Error starting Azure VM ${instanceToStart.InstanceId}: ${error.message}`;
        console.error(errorMessage);
        await this.sendNotification(errorMessage);
      }
    }

    console.log(
      `✅ Successfully started ${startedCount}/${instancesToStartCount} Azure VMs`
    );
  }

  async scaleDown(reason, runningInstances) {
    console.log(`📉 Scaling DOWN: ${reason}`);

    if (runningInstances.length <= SCALING_CONFIG.instances.minInstances) {
      console.log(
        `⚠️ Cannot scale down - already at minimum Azure VMs (${SCALING_CONFIG.instances.minInstances})`
      );
      return;
    }

    // Extract the number of instances to remove from the reason
    const instancesToRemove =
      reason.match(/can remove (\d+) instance/)?.[1] || 1;
    const instancesToStop = Math.min(
      parseInt(instancesToRemove),
      runningInstances.length - SCALING_CONFIG.instances.minInstances
    );

    // Find instances that can be stopped (respect cooldown period AND are in availableInstances list)
    const now = new Date();
    const cooldownMs = SCALING_CONFIG.instances.cooldownMinutes * 60 * 1000;

    const availableInstancesToStop = runningInstances.filter((instance) => {
      // Only consider VMs that are in our availableInstances list
      if (
        !SCALING_CONFIG.instances.availableInstances.includes(
          instance.InstanceId
        )
      ) {
        console.log(
          `⚠️ Skipping Azure VM ${instance.InstanceId} - not in availableInstances list`
        );
        return false;
      }

      const lastAction = this.lastScalingAction.get(instance.InstanceId);
      if (!lastAction) return true; // No previous action, can stop

      const timeSinceLastAction = now - lastAction.timestamp;
      return timeSinceLastAction > cooldownMs;
    });

    if (availableInstancesToStop.length === 0) {
      console.log(
        "⚠️ No Azure VMs available for scaling down (cooldown period active or no controllable VMs)"
      );
      return;
    }

    // Stop the last N VMs (keep the primary VMs running)
    const instancesToStopList = availableInstancesToStop.slice(
      -instancesToStop
    );
    console.log(
      `🛑 Stopping ${instancesToStopList.length} Azure VM(s) from ${availableInstancesToStop.length} available controllable VMs`
    );

    let stoppedCount = 0;
    for (let i = 0; i < instancesToStopList.length; i++) {
      const instanceToStop = instancesToStopList[i];
      console.log(
        `🛑 Stopping Azure VM ${i + 1}/${instancesToStopList.length}: ${
          instanceToStop.InstanceId
        } (${instanceToStop.Name})`
      );

      try {
        const result = await stopInstance(instanceToStop.InstanceId);

        if (result.success) {
          const message = `✅ Auto-scaling: Stopped Azure VM ${instanceToStop.InstanceId} (${instanceToStop.Name}) - ${reason}`;
          console.log(message);
          await this.sendNotification(message);

          // Record scaling action
          this.lastScalingAction.set(instanceToStop.InstanceId, {
            action: "stopped",
            timestamp: new Date(),
            reason,
          });

          stoppedCount++;
        } else {
          const errorMessage = `❌ Failed to stop Azure VM ${instanceToStop.InstanceId}: ${result.message}`;
          console.error(errorMessage);
          await this.sendNotification(errorMessage);
        }
      } catch (error) {
        const errorMessage = `❌ Error stopping Azure VM ${instanceToStop.InstanceId}: ${error.message}`;
        console.error(errorMessage);
        await this.sendNotification(errorMessage);
      }
    }

    console.log(
      `✅ Successfully stopped ${stoppedCount}/${instancesToStopList.length} Azure VMs`
    );
  }

  logScalingHistory(
    jobCounts,
    processingTimes,
    runningInstances,
    scalingDecision
  ) {
    const bulkJeffCount = jobCounts[SCALING_CONFIG.jobType] || 0;

    const historyEntry = {
      timestamp: new Date(),
      bulkJeffCount,
      runningInstances,
      scalingDecision,
      requiredInstances: Math.min(
        bulkJeffCount + 2,
        SCALING_CONFIG.instances.maxInstances
      ),
    };

    this.scalingHistory.push(historyEntry);

    // Keep only last 100 entries
    if (this.scalingHistory.length > 100) {
      this.scalingHistory = this.scalingHistory.slice(-100);
    }

    console.log("📋 Scaling history updated:", {
      timestamp: historyEntry.timestamp,
      bulkJeffCount: historyEntry.bulkJeffCount,
      runningInstances: historyEntry.runningInstances,
      requiredInstances: historyEntry.requiredInstances,
      action: historyEntry.scalingDecision.action,
    });
  }

  async sendNotification(message) {
    if (
      !SCALING_CONFIG.notifications.enabled ||
      !SCALING_CONFIG.notifications.slackWebhookUrl
    ) {
      return;
    }

    try {
      await sendMessageToSlack(
        SCALING_CONFIG.notifications.slackWebhookUrl,
        message
      );
    } catch (error) {
      console.error("❌ Failed to send Slack notification:", error);
    }
  }

  // Get scaling statistics for monitoring
  getScalingStats() {
    return {
      lastScalingActions: Object.fromEntries(this.lastScalingAction),
      scalingHistory: this.scalingHistory.slice(-10), // Last 10 entries
      config: SCALING_CONFIG,
    };
  }

  // Test script to list instances and provide counts
  async testInstanceStatus() {
    console.log("🔍 Testing Instance Status...");
    console.log("=" * 50);

    try {
      // Get all instances
      const instances = await listInstances();

      // Group instances by state
      const instancesByState = {};
      const controllableInstances = [];
      const nonControllableInstances = [];

      instances.forEach((instance) => {
        // Group by state
        if (!instancesByState[instance.State]) {
          instancesByState[instance.State] = [];
        }
        instancesByState[instance.State].push(instance);

        // Separate controllable vs non-controllable
        if (
          SCALING_CONFIG.instances.availableInstances.includes(
            instance.InstanceId
          )
        ) {
          controllableInstances.push(instance);
        } else {
          nonControllableInstances.push(instance);
        }
      });

      // Print summary counts
      console.log("📊 INSTANCE SUMMARY:");
      console.log(`Total Instances: ${instances.length}`);
      Object.keys(instancesByState).forEach((state) => {
        console.log(
          `${state.toUpperCase()} Instances: ${instancesByState[state].length}`
        );
      });
      console.log(`Controllable Instances: ${controllableInstances.length}`);
      console.log(
        `Non-Controllable Instances: ${nonControllableInstances.length}`
      );
      console.log("");

      // Print detailed instance list
      console.log("📋 DETAILED INSTANCE LIST:");
      console.log("-".repeat(80));
      console.log(
        "| Instance ID".padEnd(20) +
          "| Name".padEnd(25) +
          "| State".padEnd(12) +
          "| Controllable |"
      );
      console.log("-".repeat(80));

      instances.forEach((instance) => {
        const isControllable =
          SCALING_CONFIG.instances.availableInstances.includes(
            instance.InstanceId
          );
        const controllableText = isControllable ? "✅ Yes" : "❌ No";

        console.log(
          `| ${instance.InstanceId.padEnd(18)} | ${(
            instance.Name || "N/A"
          ).padEnd(23)} | ${instance.State.padEnd(
            10
          )} | ${controllableText.padEnd(11)} |`
        );
      });
      console.log("-".repeat(80));
      console.log("");

      // Print instances by state
      Object.keys(instancesByState).forEach((state) => {
        console.log(
          `🔸 ${state.toUpperCase()} INSTANCES (${
            instancesByState[state].length
          }):`
        );
        instancesByState[state].forEach((instance) => {
          const isControllable =
            SCALING_CONFIG.instances.availableInstances.includes(
              instance.InstanceId
            );
          const controllableIcon = isControllable ? "🎛️" : "🔒";
          console.log(
            `  ${controllableIcon} ${instance.InstanceId} (${
              instance.Name || "N/A"
            })`
          );
        });
        console.log("");
      });

      // Print configuration info
      console.log("⚙️ SCALING CONFIGURATION:");
      console.log(`Job Type: ${SCALING_CONFIG.jobType}`);
      console.log(`Scaling Threshold: ${SCALING_CONFIG.scalingThreshold}`);
      console.log(`Min Instances: ${SCALING_CONFIG.instances.minInstances}`);
      console.log(`Max Instances: ${SCALING_CONFIG.instances.maxInstances}`);
      console.log(
        `Cooldown Minutes: ${SCALING_CONFIG.instances.cooldownMinutes}`
      );
      console.log(
        `Available Instances for Scaling: ${SCALING_CONFIG.instances.availableInstances.join(
          ", "
        )}`
      );
      console.log("");

      // Get current job counts
      const { jobCounts, processingTimes } = await this.getJobCountsByType();
      console.log("📊 CURRENT JOB STATUS:");
      console.log(
        `${SCALING_CONFIG.jobType} Jobs (PENDING + IN_PROGRESS): ${
          jobCounts[SCALING_CONFIG.jobType] || 0
        }`
      );
      console.log("");

      // Calculate scaling decision
      const runningInstances = instances.filter(
        (inst) => inst.State === "running"
      );
      const scalingDecision = this.calculateScalingDecision(
        processingTimes,
        runningInstances.length
      );

      console.log("🎯 SCALING DECISION:");
      console.log(`Action: ${scalingDecision.action}`);
      console.log(`Reason: ${scalingDecision.reason}`);
      console.log("");

      return {
        summary: {
          total: instances.length,
          byState: Object.keys(instancesByState).reduce((acc, state) => {
            acc[state] = instancesByState[state].length;
            return acc;
          }, {}),
          controllable: controllableInstances.length,
          nonControllable: nonControllableInstances.length,
        },
        instances: instances,
        controllableInstances: controllableInstances,
        nonControllableInstances: nonControllableInstances,
        jobCounts: jobCounts,
        scalingDecision: scalingDecision,
      };
    } catch (error) {
      console.error("❌ Error testing instance status:", error);
      throw error;
    }
  }
}

// Export the worker class
module.exports = AutoScalingWorker;

// Standalone test function that can be called directly
async function runInstanceStatusTest() {
  console.log("🧪 Running Instance Status Test...");
  const worker = new AutoScalingWorker();

  try {
    const result = await worker.testInstanceStatus();
    console.log("✅ Test completed successfully!");
    return result;
  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Export the test function
module.exports.runInstanceStatusTest = runInstanceStatusTest;

// // Auto-start the worker if this is the main server and not being imported for testing
// if (process.env.SERVER_ID === "Main") {
//   console.log("🚀 Initializing Auto Scaling Worker on Main server");

//   const autoScalingWorker = new AutoScalingWorker();
//   autoScalingWorker.start().catch(console.error);

//   // Graceful shutdown
//   process.on("SIGINT", async () => {
//     console.log("\n🔄 Shutting down Auto Scaling Worker...");
//     process.exit(0);
//   });

//   process.on("SIGTERM", async () => {
//     console.log("\n🔄 Shutting down Auto Scaling Worker...");
//     process.exit(0);
//   });
// } else {
//   console.log("Not running Auto Scaling Worker - Not Main server");
// }

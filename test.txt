🚀 TESTING ALL FUNCTIONS WITH SAMPLE DATA
📊 These are the EXACT tags that will appear on your LiteLLM dashboard
================================================================================

1️⃣  TESTING: getBrandedKeyword
📝 Sample Data: Nike Running Shoes
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "PPCAudit",
  "AmazonAudit",
  "GetBrandedKeyword"
]
💰 Cost: 295 tokens
⚡ Response: Here’s a list of **branded** and **non-branded** keywords that can be used for a PPC audit for the p...

2️⃣  TESTING: getMainImageOptimisation
📝 Sample Data: Product Image Analysis
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "MainImageOptimisation",
  "AmazonAudit",
  "GetMainImageOptimisation"
]
💰 Cost: 302 tokens
⚡ Response: Analyzing your main product image and related details is a crucial step to optimize its appeal and p...

3️⃣  TESTING: getProductTitleReport (Amazon Audit)
📝 Sample Data: BSR Category Matching
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "BSRCategoryMatch",
  "AmazonAudit",
  "GetProductTitleReport"
]
💰 Cost: 256 tokens
⚡ Response: Based on the category "Electronics > Headphones > Wireless," here are some potential Best Sellers Ra...

4️⃣  TESTING: getProductTitleReport (Dynamic Audit)
📝 Sample Data: Dynamic BSR Category Matching
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "BSRCategoryMatchDynamicAudit",
  "AmazonAudit",
  "GetProductTitleReport"
]
💰 Cost: 288 tokens
⚡ Response: When dynamically auditing products within the **Sports & Outdoors > Exercise & Fitness > Cardio Trai...

5️⃣  TESTING: processAuditData (Product Title Humanisation)
📝 Sample Data: Product Title Humanization
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "ProspectProductTitleHumanisation",
  "AmazonAudit",
  "ProcessAuditData"
]
💰 Cost: 55 tokens
⚡ Response: Acme Corp Wireless Bluetooth Headphones with Noise-Canceling Features...

6️⃣  TESTING: processAuditData (BSR Category Using GPT)
📝 Sample Data: Category Detection via GPT
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "BSRCategoryUsingGPT",
  "AmazonAudit",
  "ProcessAuditData"
]
💰 Cost: 131 tokens
⚡ Response: This product can be categorized under:

**Primary Category:**  
- Electronics > Audio Equipment > He...

7️⃣  TESTING: processAuditData (Case Studies)
📝 Sample Data: Case Study Generation
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "CaseStudies",
  "AmazonAudit",
  "ProcessAuditData"
]
💰 Cost: 288 tokens
⚡ Response: ### Audit Report Case Study: Wireless Bluetooth Headphones  
**Product Category**: Electronics  
**P...

8️⃣  TESTING: fetchCompAmazonData
📝 Sample Data: Competition Search Keywords
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "CompSearchKeyword",
  "CompetitionData",
  "FetchCompAmazonData"
]
💰 Cost: 297 tokens
⚡ Response: Here are search keywords for competitor analysis related to "Wireless Bluetooth Headphones":

### Ge...

9️⃣  TESTING: processCompetitionData (Company Name Humanisation)
📝 Sample Data: Competitor Company Name
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "CompetitorCompanyNameHumanisation",
  "CompetitionData",
  "ProcessCompetitionData"
]
💰 Cost: 81 tokens
⚡ Response: Sure! Here's a more humanized and approachable alternative for "Sony Electronics Corporation Ltd.":
...

🔟 TESTING: processCompetitionData (Product Title Humanisation)
📝 Sample Data: Competitor Product Title
--------------------------------------------------
🏷️  DASHBOARD TAGS: [
  "Jeff",
  "ScrapeGPT",
  "GetChatGPTResponse",
  "ClientId:1",
  "CompetitorProductTitleHumanisation",
  "CompetitionData",
  "ProcessCompetitionData"
]
💰 Cost: 122 tokens
⚡ Response: - Sony WH-1000XM4 Wireless Noise-Canceling Over-Ear Headphones  
- Sony XM4 Bluetooth Headphones wit...

📊 DASHBOARD SUMMARY
================================================================================
🎯 These are the EXACT tags you will see on your LiteLLM dashboard:

1. getBrandedKeyword:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, PPCAudit, AmazonAudit, GetBrandedKeyword]
   💰 Tokens: 295

2. getMainImageOptimisation:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, MainImageOptimisation, AmazonAudit, GetMainImageOptimisation]
   💰 Tokens: 302

3. getProductTitleReport_amazonAudit:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, BSRCategoryMatch, AmazonAudit, GetProductTitleReport]
   💰 Tokens: 256

4. getProductTitleReport_dynamicAudit:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, BSRCategoryMatchDynamicAudit, AmazonAudit, GetProductTitleReport]
   💰 Tokens: 288

5. processAuditData_productTitleHumanisation:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, ProspectProductTitleHumanisation, AmazonAudit, ProcessAuditData]
   💰 Tokens: 55

6. processAuditData_bsrCategoryUsingGpt:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, BSRCategoryUsingGPT, AmazonAudit, ProcessAuditData]
   💰 Tokens: 131

7. processAuditData_caseStudies:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, CaseStudies, AmazonAudit, ProcessAuditData]
   💰 Tokens: 288

8. fetchCompAmazonData:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, CompSearchKeyword, CompetitionData, FetchCompAmazonData]
   💰 Tokens: 297

9. processCompetitionData_companyNameHumanisation:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, CompetitorCompanyNameHumanisation, CompetitionData, ProcessCompetitionData]
   💰 Tokens: 81

10. processCompetitionData_productTitleHumanisation:
   🏷️  Tags: [Jeff, ScrapeGPT, GetChatGPTResponse, ClientId:1, CompetitorProductTitleHumanisation, CompetitionData, ProcessCompetitionData]
   💰 Tokens: 122

🔍 TAG BREAKDOWN:
   🔧 Internal Tags (Auto-added): Jeff, ScrapeGPT, GetChatGPTResponse
   🏢 Business Tags (Your custom): ClientId:X, FeatureTag, ServiceContext, FunctionContext

💰 Total Test Cost: 2115 tokens
✅ Successful Tests: 10/10
